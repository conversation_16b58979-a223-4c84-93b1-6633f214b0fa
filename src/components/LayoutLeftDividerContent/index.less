@left-divider-content: .left-divider-content;
@gray-border-color: #e8e9eb;
@hover-border-color: #2468F2;

.divider-merge() {
  @{left-divider-content}-divider__line {
    width: 3px;
    background-color: @hover-border-color;
  }

  @{left-divider-content}-divider__icon {
    display: block !important;

    > svg {
      margin-top: 1px;
    }
  }
}

@{left-divider-content} {
  display: flex;

  &-l {
    resize: none;
  }

  &-r {
    flex: 1;
    min-width: 0;
  }

  &-divider {
    position: relative;
    border-right: 1px solid @gray-border-color;

    &__line {
      position: absolute;
      width: 2px;
      height: 100%;
      cursor: col-resize;
      transition: background-color 0.2s;
    }

    &__icon {
      width: 20px;
      height: 20px;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      border-radius: 50%;
      border: 1px solid @gray-border-color;
      background-color: #fff;
      font-size: 12px;
      z-index: 1;
      cursor: pointer;
      display: none;
    }

    &:hover {
      opacity: 1;
      width: 2px;

      .divider-merge();
    }
  }

  &-divider&-dragging {
    display: block;
    opacity: 1;

    .divider-merge();
  }

  &-divider&-zero {
    display: block;
    opacity: 1;
    width: 10px;

    @{left-divider-content}-divider__line {
      right: 0;
    }

    @{left-divider-content}-divider__icon {
      transform: translate(-50%, -50%) rotate(180deg);
      display: block !important;
    }
  }
}
