import {BaseResponseType, request, urlPrefix} from './apiFunction';
import {AxiosProgressEvent} from 'axios';
interface GetWorkspaceFolderListParams {
  workspaceId: string;
  parentId: string;
}

export interface GetWorkspaceFolderListResult {
  id: string;
  name: string;
  path: string;
  parentId: string;
  creator: string;
  createdAt: string;
  type: string;
}

export function getWorkspaceFolderList(
  params: GetWorkspaceFolderListParams
): BaseResponseType<GetWorkspaceFolderListResult[]> {
  return request({
    url: `${urlPrefix}/workspaces/${params.workspaceId}/dirs/list`,
    method: 'GET',
    params: {
      parentId: params.parentId
    }
  });
}

interface GetWorkspaceFileListParams {
  workspaceId: string;
  parentId: string;
  fileName?: string;
  order?: 'asc' | 'desc';
  orderBy?: string;
}

export interface GetWorkspaceFileListResult {
  id: string;
  name: string;
  path: string;
  parentId: string;
  nodeType: 'FILE' | 'FOLDER' | 'TRASH';
  creator: string;
  createdAt: string;
  type: 'HOME' | 'USERS' | 'TRASH' | 'ALL' | 'NORMAL';
}

export function getWorkspaceFileList(
  params: GetWorkspaceFileListParams
): BaseResponseType<GetWorkspaceFileListResult[]> {
  const {workspaceId, parentId, fileName, order, orderBy} = params;
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/files/list`,
    method: 'GET',
    params: {
      parentId,
      fileName,
      order,
      orderBy
    }
  });
}

interface CreateWorkspaceFolderParams {
  name: string;
  parentId: string;
  workspaceId: string;
}

export function createWorkspaceFolder(
  params: CreateWorkspaceFolderParams
): BaseResponseType<GetWorkspaceFolderListResult> {
  const {name, parentId, workspaceId} = params;
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/dirs`,
    method: 'POST',
    data: {
      name,
      parentId
    }
  });
}

export function uploadFile(params: {
  workspaceId: string;
  parentId: string;
  formData: FormData;
  onProgress?: (event: AxiosProgressEvent) => void;
}): BaseResponseType<any> {
  const {workspaceId, parentId, formData, onProgress} = params;
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/files`,
    method: 'POST',
    params: {
      parentId
    },
    data: formData,
    onUploadProgress: onProgress
  });
}

interface CopyFileParams {
  workspaceId: string;
  id: string;
  name?: string;
  parentId: string;
}

export function copyFile(params: CopyFileParams) {
  const {workspaceId, id, parentId, name} = params;
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/files/${id}`,
    method: 'POST',
    data: {
      parentId,
      name
    }
  });
}

export function copyFolder(params: CopyFileParams) {
  const {workspaceId, id, parentId, name} = params;
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/dirs/${id}`,
    method: 'POST',
    data: {
      parentId,
      name
    }
  });
}

interface RenameFileParams {
  workspaceId: string;
  id: string;
  name?: string;
}

export function renameFolder(params: RenameFileParams) {
  console.log('renameFolder', params);
  const {workspaceId, id, name} = params;
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/dirs/${id}`,
    method: 'PATCH',
    data: {
      name
    }
  });
}

export function renameFile(params: RenameFileParams) {
  console.log('renameFile', params);
  const {workspaceId, id, name} = params;
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/files/${id}`,
    method: 'PATCH',
    data: {
      name
    }
  });
}

export function moveFile(params: {id: string; parentId: string; workspaceId: string}) {
  console.log('moveFile', params);

  const {id, parentId, workspaceId} = params;
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/files/${id}`,
    method: 'PATCH',
    data: {
      parentId,
      action: 'MOVE'
    }
  });
}

export function moveFolder(params: {id: string; parentId: string; workspaceId: string}) {
  const {id, parentId, workspaceId} = params;
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/dirs/${id}`,
    method: 'PATCH',
    data: {
      parentId,
      action: 'MOVE'
    }
  });
}

export function deleteFile(params: {id: string; workspaceId: string}) {
  const {id, workspaceId} = params;
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/files/${id}`,
    method: 'PATCH',
    data: {
      parentId: 'TRASH',
      action: 'DELETE'
    }
  });
}

export function deleteFolder(params: {id: string; workspaceId: string}) {
  const {id, workspaceId} = params;
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/dirs/${id}`,
    method: 'PATCH',
    data: {
      parentId: 'TRASH',
      action: 'DELETE'
    }
  });
}

export function restoreFile(params: {id: string; parentId: string; workspaceId: string}) {
  const {id, parentId, workspaceId} = params;

  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/files/${id}`,
    method: 'PATCH',
    data: {
      parentId,
      action: 'RESTORE'
    }
  });
}

export function restoreFolder(params: {id: string; parentId: string; workspaceId: string}) {
  const {id, parentId, workspaceId} = params;

  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/dirs/${id}`,
    method: 'PATCH',
    data: {
      parentId,
      action: 'RESTORE'
    }
  });
}

export function permanentDeleteFile(params: {id: string; workspaceId: string}) {
  const {id, workspaceId} = params;
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/files/${id}`,
    method: 'DELETE'
  });
}

export function permanentDeleteFolder(params: {id: string; workspaceId: string}) {
  const {id, workspaceId} = params;
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/dirs/${id}`,
    method: 'DELETE'
  });
}

export function getFolderPath(params: {
  id: string;
  workspaceId: string;
}): BaseResponseType<GetWorkspaceFolderListResult[]> {
  const {id, workspaceId} = params;
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/dirs/${id}/path`,
    method: 'GET'
  });
}
