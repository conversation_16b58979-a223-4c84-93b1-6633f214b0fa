import React, {ReactNode, useMemo, useCallback} from 'react';
import {Avatar, Dropdown, Menu} from 'acud';
import {AvatarProps} from 'acud/lib/avatar';
import {MenuProps} from 'acud/lib/menu/menu';
import {BaseDropdownProps} from 'acud/lib/dropdown/dropdown';

import styles from './index.module.less';

export interface IMenuItem {
  // menu 展示内容
  label: ReactNode;
  // menu key
  key: string;
  // 图标，默认16*16
  icon?: ReactNode;
  // TODO 二级菜单，暂不展示
  children?: IMenuItem[];
  // 点击回调
  clickHandler?: () => void;
}
export interface IAvatarDropdownProps {
  /** 用户名 */
  userName: string;
  /** 下拉菜单列表 */
  menuList?: IMenuItem[];
  /** 是否展示header */
  header?: boolean;
  /** 自定义header */
  customHeader?: ReactNode;
  /**
   * @description 头像属性
   * @type {AvatarProps}
   */
  avatarProps?: AvatarProps;
  /**
   * @description 菜单栏属性
   * @type {MenuProps}
   */
  menuProps?: MenuProps;
  /**
   * @description Dropdown属性
   * @type {BaseDropdownProps} acud
   */
  dropdownProps?: Omit<BaseDropdownProps, 'overlay'>;
  /** 登出回调 */
  logout?: () => void;
}

const klass = 'db-avatar-dropdown';
const {MenuHead} = Menu;

/**
 * 头像及下拉菜单组件
 * @param {string} userName 用户名
 * @param {MenuItem[]} menuList 下拉菜单列表
 * @param {Boolean} header 是否展示头部用户信息
 * @param {ReactNode} customHeader 用户自定义头部
 * @param {() => void} logout 登出回调
 * @param {AvatarProps} avatarProps 自定义头像参数
 * @param {MenuProps} menuProps 自定义菜单参数
 * @param {BaseDropdownProps} dropdownProps 下拉组件参数
 */
const AvatarDropdown: React.FC<IAvatarDropdownProps> = ({
  userName,
  menuList,
  header,
  customHeader,
  logout,
  avatarProps = {},
  menuProps = {},
  dropdownProps = {}
}) => {
  // 头像
  const avatar = useMemo(() => {
    return (
      <Avatar size="small" className={styles[`${klass}-avatar`]} {...avatarProps}>
        {/** 没有头像自定义参数，采取名称首字母作为头像 */}
        {!Object.keys(avatarProps).length && userName[0]}
      </Avatar>
    );
  }, [avatarProps, userName]);

  // 菜单头部
  const menuHeaderContent = useMemo(() => {
    if (customHeader) {
      return customHeader;
    }
    return (
      <div className={styles[`${klass}-menu-header-content`]}>
        {avatar}
        <span className={styles[`${klass}-menu-header-name`]}>{userName}</span>
      </div>
    );
  }, [avatar, customHeader, userName]);

  // 菜单项点击回调
  const onMenuItemClick = useCallback(
    (item: IMenuItem) => () => {
      if (item?.clickHandler) {
        item?.clickHandler();
      }
    },
    []
  );

  // 渲染menuItem及group
  const renderMenuItem = useCallback(
    (item: IMenuItem) => {
      if (item?.children?.length) {
        const menu = <Menu>{item.children.map((item) => renderMenuItem(item))}</Menu>;
        return (
          <Menu.Item key={item.key} icon={item.icon}>
            <Dropdown overlay={menu} label={item.label} placement="leftTop" />
          </Menu.Item>
        );
      }
      return (
        <Menu.Item key={item.key} onClick={onMenuItemClick(item)} icon={item.icon}>
          {item.label}
        </Menu.Item>
      );
    },
    [onMenuItemClick]
  );

  // 下拉菜单
  const dropdownMenu = useMemo(() => {
    return (
      <Menu mode="inline" {...menuProps}>
        {header && (
          <MenuHead>
            <div className={styles[`${klass}-menu-header`]}>{menuHeaderContent}</div>
          </MenuHead>
        )}
        {menuList?.map((item) => renderMenuItem(item))}
        {logout && (
          <div onClick={logout} className={styles[`${klass}-menu-footer`]}>
            退出登录
          </div>
        )}
      </Menu>
    );
  }, [header, logout, menuHeaderContent, menuList, menuProps, renderMenuItem]);

  return (
    <div className={styles[klass]}>
      <Dropdown
        {...dropdownProps}
        overlay={dropdownMenu}
        label={avatar}
        overlayClassName={styles[`${klass}-menu`]}
      />
    </div>
  );
};
export default AvatarDropdown;
