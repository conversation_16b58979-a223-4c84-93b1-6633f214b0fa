/**
 * DescriptionList 组件
 *
 * 该组件用于展示描述列表，以 key:value 的形式显示一组信息。
 * 每行信息由一个标签和一个对应的值组成，使用 `Row` 和 `Col` 布局组件进行排列。
 *
 * <AUTHOR>
 */

import React from 'react';
import {Col, Row} from 'acud';

import styles from './index.module.less';
/**
 * 描述列表组件的属性
 * @interface IDescriptionListProps
 * @property {Array} infoList - 描述列表数据
 * @property {boolean} bordered - 是否显示边框 默认 false
 * @property {boolean} colon - 是否显示冒号 默认 true
 * @property {number} span - 默认24
 * @property {string} flex - 标签宽度 默认 0 0 100px
 */
interface IDescriptionListProps {
  infoList: {label: string; value: string | React.ReactNode; span?: number}[];
  colon?: boolean;
  span?: number;
  flex?: string;
  className?: string;
}

const klass = 'description-list-main';

const DescriptionList = (props: IDescriptionListProps) => {
  const {infoList = [], colon = true, span = 24, flex = '0 0 100px', className} = props;
  return (
    <Row className={`${styles[klass]} ${className}`}>
      {infoList.map((item) => (
        <Col span={item.span || span} key={item.label} className={styles[`${klass}-col`]}>
          <div style={{flex}} className={styles[`${klass}-label`]}>
            {item.label}
            {colon && ':'}
          </div>
          <div style={{flex: '1 1 auto'}} className={styles[`${klass}-value`]}>
            {item.value || '-'}
          </div>
        </Col>
      ))}
    </Row>
  );
};

export default React.memo(DescriptionList);
