/**
 * iframe 内嵌 EDAP 页面
 */

import React, {FC, useEffect, useLayoutEffect} from 'react';
import {useLocation} from 'react-router-dom';
import {Loading} from 'acud';
import {useIframePreloader} from '@hooks/useIframePreloader';
import './index.less';
import {hideIframe, showIframe} from '@components/IframePreloader';

const IframeEdapPageView: FC = () => {
  useLayoutEffect(() => {
    showIframe('edap-iframe', document.getElementById('iframe-div-box') as Element);
    return () => {
      hideIframe('edap-iframe');
    };
  }, []);

  return (
    <div className="iframe-page-box">
      {/* {isLoading && (
        <div className="iframe-loading-overlay">
          <Loading loading />
        </div>
      )} */}
      {/* 占位容器，实际的iframe会被动态移动到这里 */}
      <div
        id="iframe-div-box"
        className="iframe-div-box"
        style={{
          width: '100%',
          height: '100%',
          // opacity: isLoaded ? 1 : 0,
          transition: 'opacity 0.3s ease-in-out'
        }}
      />
    </div>
  );
};

export default React.memo(IframeEdapPageView);
