/**
 * MetaBreadcrumb组件 - RightContent 顶部面包屑导航
 * <AUTHOR>
 */

import {useCallback} from 'react';
import {Breadcrumb} from 'acud';

import {IUrlStateHandler} from '../index';

import styles from './MetaBreadcrumb.module.less';

const MetaBreadcrumb = (props: IUrlStateHandler) => {
  const {urlState = {}, changeUrlFun} = props;
  const {catalog, schema, type, node} = urlState;

  const breadcrumbList = [catalog, schema, node].filter((item) => item !== undefined && item !== '');

  // 修改 url 参数
  const onChangeUrlQuery = useCallback(
    (value: string, index) => {
      if (index === breadcrumbList.length - 1) {
        return;
      }
      changeUrlFun({
        catalog: index >= 0 ? catalog : undefined,
        schema: index >= 1 ? schema : undefined,
        type: index <= 1 ? undefined : type,
        node: index <= 1 ? undefined : node,
        tab: undefined,
        path: undefined
      });
    },
    [catalog, changeUrlFun, node, schema, type, breadcrumbList]
  );

  return (
    <div className={styles['meta-breadcrumb']}>
      <Breadcrumb>
        <Breadcrumb.Item key="00">
          <span></span>
        </Breadcrumb.Item>
        {breadcrumbList.map((item, index) => {
          return (
            <Breadcrumb.Item key={index}>
              <a
                href="#"
                onClick={(e) => {
                  e.preventDefault();
                  onChangeUrlQuery(item as string, index);
                }}
              >
                {item}
              </a>
            </Breadcrumb.Item>
          );
        })}
      </Breadcrumb>
    </div>
  );
};

export default MetaBreadcrumb;
