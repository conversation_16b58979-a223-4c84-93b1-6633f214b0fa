.workspace-wrapper {
  flex: 1;
  min-width: 0;
  height: 100%;
  position: relative;
  background-color: #fff;
  padding: 16px;
  box-sizing: border-box;
  overflow-y: auto;

  .workspace-wrapper-header {
    margin-bottom: 12px;
    font-family: PingFangSC-Medium;
    font-size: 18px;
    color: #151b26;
    line-height: 32px;
  }

  .container-clipboard {
    display: inline-block;
  }

  :global {
    .acud-btn {
      border-radius: 6px;
    }

    .acud-table-wrapper .acud-table-empty {
      min-height: 60vh;
      .acud-table-body {
        overflow: hidden !important;
      }
      .acud-empty {
        min-height: 50vh;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-flow: column;
      }
    }

    .clip-board-container {
      display: none;
      height: 20px;

      button {
        height: 20px;
      }
    }

    .acud-table-cell-row-hover {
      .clip-board-container {
        display: block;
      }
    }

    .acud-table-cell-ellipsis {
      .toolkit-ellipsis {
        display: block;
      }
    }

    .acud-dropdown-trigger {
      display: inline-flex;
      align-items: center;
    }
  }

  .workspace-status {
    font-family: PingFangSC-Regular;
    border-radius: 10px;
    line-height: 18px;
    padding-inline: 8px;
    display: inline-flex;
    align-items: center;

    svg {
      margin-left: 4px;
    }
  }

  .workspace-status.ERROR {
    background: #feedea;
    color: #f33e3e;
  }

  .workspace-status.RUNNING {
    background: #e6f0ff;
    color: #2468f2;
  }

  .td-operation-bar {
    display: flex;
    align-items: center;

    .td-operation-open {
      display: flex;
      align-items: center;
      margin-right: 15.5px;

      svg {
        margin-left: 4px;
      }
    }

    .td-operation-more {
      border-radius: 4px;

      &:hover {
        background-color: rgba(7, 12, 20, 0.06);
      }
    }
  }

  .empty-container {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    .empty-block {
      display: flex;
      flex-flow: column;
      align-items: center;
      justify-content: center;

      .empty-title {
        font-size: 28px;
      }
    }
  }

  .empty-title {
    font-family: PingFangSC-Medium;
    font-size: 24px;
    color: #2d2e2e;
    text-align: center;
    line-height: 36px;
  }

  .empty-desc {
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #5c5f66;
    text-align: center;
    line-height: 20px;
    margin: 16px 0 24px;
  }

  .empty-img {
    margin-top: 24px;
  }

  .operation-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;

    .left-btn-container {
      display: flex;
      align-items: center;

      button {
        margin-right: 16px;
      }
    }

    .right-container {
      display: flex;
      align-items: center;
      gap: 10px;

      .search-container {
        width: 240px;
      }
    }
  }

  .pagination-container {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;

    :global {
      .acud-pagination {
        width: fit-content;
      }
    }
  }
}


.large-tooltip-width600 {
  max-width: 600px;
}
