import React from 'react';
import classNames from 'classnames/bind';
import styles from './index.module.less';

const cx = classNames.bind(styles);

interface PayOption {
  value: string;
  label: string;
  desc: string;
  disabled?: boolean;
  className?: string;
}

interface PaySelectProps {
  value?: string;
  options: PayOption[];
  onChange?: (value: string) => void;
}

const PaySelect: React.FC<PaySelectProps> = ({value, options, onChange}) => {
  return (
    <div className={cx('pay-select')}>
      {options.map((option) => (
        <div
          key={option.value}
          className={cx(
            'pay-option',
            {
              selected: value === option.value,
              disabled: option.disabled
            },
            option.className
          )}
          onClick={() => !option.disabled && onChange?.(option.value)}
        >
          <div className={cx('pay-option-icon')}></div>
          <div className={cx('pay-option-label')}>
            <div className={cx('pay-option-label-text')}>{option.label}</div>
            <div className={cx('pay-option-label-desc')}>{option.desc}</div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default PaySelect;
