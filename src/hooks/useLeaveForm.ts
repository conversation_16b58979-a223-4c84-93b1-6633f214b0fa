import {Modal} from 'acud';
import {useEffect} from 'react';
import {useBlocker} from 'react-router-dom';

/**  离开页面 提示信息 目前只监听了 navigate  跳转事件
 *
 * @param isEditing 是否编辑 否的话 不提示
 * @param title 提示标题
 * @param content 提示内容
 * @returns
 */

export interface ILeaveFormProps {
  isEditing?: boolean;
  okText?: string;
  cancelText?: string;
  title?: string;
  content?: string;
  onOk?: () => Promise<boolean>;
  onCancel?: () => void;
}
export default function useLeaveForm({
  isEditing = false,
  okText = '保存并关闭',
  cancelText = '立即关闭',
  title = '提示',
  content = '你还有未保存的更改,如果现在关闭，未保存的数据内容将丢失，请确认是否要离开？',
  onOk = () => Promise.resolve(true),
  onCancel
}: ILeaveFormProps) {
  const blocker = useBlocker(
    ({currentLocation, nextLocation}) => isEditing && currentLocation.pathname !== nextLocation.pathname
  );

  useEffect(() => {
    if (blocker.state === 'blocked') {
      Modal.confirm({
        title,
        content,
        okText,
        cancelText,
        onOk: async () => {
          const res = await onOk();
          if (res) {
            blocker.proceed();
          }
        },
        onCancel: () => {
          blocker.reset();
        }
      });
    }
  }, [blocker, title, content]);

  // const handleHashChange = useMemoizedFn((event: HashChangeEvent) => {
  //   if (isEditing) {
  //     console.log('hashchange', event.oldURL, event.newURL);
  //     Modal.confirm({
  //       title: '提示blocker',
  //       content: desc,
  //       onOk: () => {},
  //       onCancel: () => {}
  //     });
  //   }
  // });

  // useEffect(() => {
  //   window.addEventListener('hashchange', handleHashChange);
  //   return () => {
  //     window.removeEventListener('hashchange', handleHashChange);
  //   };
  // }, [isEditing]);
}
