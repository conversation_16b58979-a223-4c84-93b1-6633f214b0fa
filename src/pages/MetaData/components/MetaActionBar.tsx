/**
 * MetaActionBar组件 - Panel的顶部操作栏
 * <AUTHOR>
 */

import React from 'react';
import {Button, Dropdown, Menu, Space} from 'acud';
import {More} from '@baidu/xicon-react-bigdata';

import styles from './MetaAction.module.less';
import {CatalogType} from '../index';

type IMenuItem = {
  key: string; // 唯一 key
  label: string; // 下拉项文本
  disabled?: boolean; // 是否禁用该下拉项
};
interface IActionBase {
  catalog: CatalogType.SYSTEM | string; // 当 catalog 为 内置的 system 时，禁用相关操作
  icon: React.ReactNode; // 标题左侧图标
  title: string; // 标题
  dropdownMenu: IMenuItem[]; // 下拉操作列
  onDropdownClick: (key: string) => void; // 下拉操作列点击事件
  createMenu?: IMenuItem[]; // 创建按钮下拉列
}

interface IAvtionNoCreate extends IActionBase {
  hiddenCreate: true; // 无新建按钮（可选）
  createText?: any; // 确保 createText 不存在
  createIcon?: React.ReactNode; // 新建按钮图标
  onCreateClick?: any; // 确保 onCreateClick 不存在
}

interface IActionHasCreate extends IActionBase {
  hiddenCreate?: false; // 有新建按钮（可选）
  createText: string; // 新建按钮文本
  createIcon?: React.ReactNode; // 新建按钮图标
  onCreateClick: (key?: string) => void; // 新建按钮点击事件
}

type IMetaActionBar = IActionHasCreate | IAvtionNoCreate;

// 下拉按钮 list 渲染
const MenuEl = (props: {menu: IMenuItem[]; onClick: (key: string) => void; isDisabled: boolean}) => {
  const {menu, onClick, isDisabled} = props;

  const onClickMenuItem = (e: any) => {
    onClick(e.key);
  };

  return (
    <Menu onClick={onClickMenuItem}>
      {menu.map((item: IMenuItem) => (
        <Menu.Item key={item.key} disabled={item.disabled || isDisabled}>
          {item.label}
        </Menu.Item>
      ))}
    </Menu>
  );
};

const MetaActionBar = (props: IMetaActionBar) => {
  const {
    catalog,
    icon,
    title,
    dropdownMenu,
    onDropdownClick,
    hiddenCreate,
    createText,
    createMenu,
    onCreateClick,
    createIcon
  } = props;

  // 内置的 system 时，禁用相关操作
  const isDisabled = catalog === CatalogType.SYSTEM;

  return (
    <div className={styles['meta-action']}>
      <div className={styles['meta-action-title']}>
        <span>{icon}</span>
        {title}
      </div>
      <div className={styles['meta-action-button-group']}>
        <Dropdown overlay={MenuEl({menu: dropdownMenu, onClick: onDropdownClick, isDisabled})}>
          <Space>
            <Button className={styles['meta-action-button']}>
              <More theme="line" color="#6c6d70" size={16} strokeLinejoin="round" />
            </Button>
          </Space>
        </Dropdown>
        {hiddenCreate ? null : createMenu?.length ? (
          <div>
            <Dropdown overlay={MenuEl({menu: createMenu, onClick: onCreateClick, isDisabled})}>
              <Space>
                <Button type="primary" icon={createIcon}>
                  立即新建
                </Button>
              </Space>
            </Dropdown>
          </div>
        ) : (
          <Button type="primary" onClick={() => onCreateClick()} disabled={isDisabled} icon={createIcon}>
            {createText}
          </Button>
        )}
      </div>
    </div>
  );
};

export default React.memo(MetaActionBar);
