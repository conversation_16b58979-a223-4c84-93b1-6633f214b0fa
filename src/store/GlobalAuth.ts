import {PermissionType} from '@api/auth';
import {createSlice} from '@reduxjs/toolkit';

export interface IGlobalAuthState {
  workspace: PermissionType;
  metastore: PermissionType;
}

const initialState: IGlobalAuthState = {
  workspace: undefined,
  metastore: undefined
};

const globalAuthSlice = createSlice({
  name: 'globalAuth',
  initialState,
  reducers: {
    updateGlobalAuth: (state, action) => {
      ['workspace', 'metastore'].forEach((key) => {
        state[key] = action.payload[key]?.[0];
      });
    }
  }
});

export const {updateGlobalAuth} = globalAuthSlice.actions;

export default globalAuthSlice.reducer;
