.result-page {
  width: 100%;
  height: 100%;
  padding: 0 8px 8px 0;
  overflow: hidden;
  box-sizing: border-box;

  .result-container {
    width: 100%;
    height: 100%;
    background-color: #fff;
    border: 1px solid rgba(212, 214, 217, 0.6);
    border-radius: 6px;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    overflow: hidden;
    .header {
      padding: 12px;
      border-bottom: 1px solid #e8e8e8;
      a {
        font-size: 12px;
      }
      .title {
        margin-top: 14px;
        margin-bottom: 4px;
        font-size: 22px;
        color: #151b26;
        line-height: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        gap: 8px;
        .title-ellipsis {
          max-width: 50%;
        }
        .title-tag {
          border-radius: 12px;
        }
        // // 标签为纯圆角
        // :global {
        //   .acud-tag-md {
        //     border-radius: 12px;
        //   }
        // }
      }
      .description {
        font-size: 12px;
        color: #84868c;
        line-height: 24px;
        .icon-text {
          display: flex;
          align-items: center;
          gap: 4px;
        }
      }
    }
    .content {
      overflow: hidden;
      flex: 1;
      .content-row {
        height: 100%;
        .content-table {
          height: 100%;
          overflow: hidden;
        }
      }
    }
  }
}
