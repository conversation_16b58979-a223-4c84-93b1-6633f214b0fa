.template-detail {
  display: flex;
  flex-direction: column;
  .breadcrumb {
    font-size: 12px;
    color: #84868c;
    line-height: 20px;
    margin-bottom: 14px;
  }

  .loading-container {
    width: 100%;
    min-height: 500px;
  }

  .operate {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .name {
      font-size: 22px;
      font-weight: bold;
      line-height: 32px;
      color: #151B26;
      .icon {
        margin-right: 8px;
        vertical-align: top;
      }
    }

    .right-options {
      :global {
        .acud-btn {
          margin-left: 10px;
        }
      }
    }
  }

  .workflow-x6 {
    padding-top: 16px;
    width: 100%;
    flex: 1;
  }
}
