/**
 * @file schema - 新建Volume弹窗
 * <AUTHOR>
 */

import {FC, useCallback, useState} from 'react';
import {Form, Input, Link, Modal, toast, Radio} from 'acud';
import {EVolumeType, createVolume, IVolumeParams, getVolumeDetail} from '@api/metaRequest';
import {RULE} from '@utils/regs';
import {IUrlStateHandler} from '../index';
import styles from './CreateVolumeModal.module.less';

const VolumeTypeMap = {
  [EVolumeType.MANAGED]: 'Managed volume',
  [EVolumeType.EXTERNAL]: 'External volume'
};

interface ICreateVolumeModalProps {
  /** url参数信息 */
  urlState: IUrlStateHandler['urlState'];
  /** 弹窗是否打开 */
  isModalVisible: boolean;
  /**
   * 创建成功的回调函数
   * @param name 当前 volume 名称
   * @returns
   */
  createdCallback: (name: string) => void;
  /** 关闭弹窗 */
  handleCloseModal: () => void;
}

const CreateVolumeModal: FC<ICreateVolumeModalProps> = ({
  urlState,
  isModalVisible,
  createdCallback,
  handleCloseModal
}) => {
  const {catalog = '', schema = ''} = urlState;
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const volumeType = Form.useWatch('volumeType', form);

  // 关闭弹窗
  const onCloseModal = useCallback(() => {
    handleCloseModal();
    form.resetFields();
  }, [form, handleCloseModal]);

  // 提交表单
  const handleConfirm = useCallback(() => {
    // 表单校验
    form
      .validateFields()
      .then((values) => {
        const params: IVolumeParams = {
          ...values,
          catalogName: catalog,
          schemaName: schema,
          ...(values.storageLocation ? {storageLocation: 'bos://' + values.storageLocation} : {})
        };
        setLoading(true);

        // 创建volume
        createVolume(params)
          .then((res) => {
            if (res?.success) {
              toast.success({
                message: '创建成功',
                duration: 5
              });

              onCloseModal();
              // 调用成功的回调方法
              createdCallback(values.name);
            }
          })
          .finally(() => {
            setLoading(false);
          });
      })
      .catch(() => {});
  }, [form, catalog, schema, onCloseModal, createdCallback]);

  // 跳转accesslist页面
  const jumpAccesslist = () => {
    window.open('/iam/#/iam/accesslist', '_blank');
  };

  return (
    <Modal
      closable={true}
      title="创建 Volume"
      width={500}
      visible={isModalVisible}
      onOk={handleConfirm}
      onCancel={onCloseModal}
      okButtonProps={{loading}}
      destroyOnClose={true}
      className={styles['create-volume-modal']}
    >
      <Form
        labelAlign="left"
        layout="vertical"
        colon={false}
        labelWidth={80}
        initialValues={{volumeType: EVolumeType.MANAGED}}
        form={form}
      >
        <Form.Item
          label="Volume名称"
          name="name"
          validateFirst
          validateDebounce={1000}
          rules={[
            {required: true, message: '请输入Volume名称'},
            {
              validator: async (_, value) => {
                // 校验特殊字符和长度限制
                if (!RULE.specialName64.test(value)) {
                  return Promise.reject(new Error(RULE.specialName64Text));
                }
                // 异步校验Volume名称是否重复，复用查询接口 silent模式
                const res = await getVolumeDetail(`${catalog}.${schema}.${value}`, true);
                if (res.success && res.result?.id) {
                  return Promise.reject(new Error('该Volume名称已存在，请重新输入'));
                }
                return Promise.resolve();
              }
            }
          ]}
        >
          <Input placeholder="请输入Volume名称" allowClear limitLength={64} forbidIfLimit={true} />
        </Form.Item>

        <Form.Item
          label="Volume类型"
          name="volumeType"
          required
          tooltip="Managed volume的数据存储在工作空间的元存储中，External volume需要用户挂载其他BOS路径，数据存储在对应BOS中"
        >
          <Radio.Group>
            {Object.keys(VolumeTypeMap).map((key) => (
              <Radio value={key} key={key}>
                <span>{VolumeTypeMap[key]}</span>
              </Radio>
            ))}
          </Radio.Group>
        </Form.Item>
        {volumeType === EVolumeType.EXTERNAL && (
          <>
            <Form.Item
              label="BOS路径"
              name="storageLocation"
              rules={[
                {required: true, message: '请输入BOS路径'},
                {pattern: RULE.bos, message: RULE.bosText},
                {
                  validator: async (_, value) => {
                    if (value.length > 494) {
                      return Promise.reject(new Error('BOS路径长度不能超过500'));
                    }
                    return Promise.resolve();
                  }
                }
              ]}
              keepDisplayExtra
              extra={
                <div>
                  <span>BOS路径需要和工作空间在同一个地域</span>
                </div>
              }
            >
              <Input placeholder="请输入BOS路径" addonBefore="bos://" />
            </Form.Item>
            <Form.Item
              label="AccessKey"
              name="accessKeyId"
              rules={[{required: true, message: '请输入AccessKey'}]}
              keepDisplayExtra
              extra={
                <div>
                  <span>建议AK/SK有BOSFullControl权限，权限不足会影响Volume的读写操作。</span>
                  <Link onClick={jumpAccesslist}>立即获取</Link>
                </div>
              }
            >
              <Input placeholder="请输入AccessKey" allowClear />
            </Form.Item>

            <Form.Item
              label="SecretKey"
              name="secretAccessKey"
              rules={[{required: true, message: '请输入SecretKey'}]}
            >
              <Input placeholder="请输入SecretKey" allowClear />
            </Form.Item>
          </>
        )}
        <Form.Item label="描述" name="comment">
          <Input.TextArea
            placeholder="请输入Volume描述"
            allowClear
            limitLength={150}
            autoSize={{minRows: 2, maxRows: 4}}
            forbidIfLimit
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default CreateVolumeModal;
