import {JobTaskType} from '@pages/JobWorkflow/constants';

/**
 *
 * 处理任务
 * @returns
 */
export const dealTaskParams = (params: any) => {
  if (params.type === JobTaskType.RAY_TASK) {
    const taskParam = params.taskParam;
    const cluster = taskParam?.clusterList[0];
    const result = [];
    result.push({label: '运行代码路径', value: taskParam?.codePath});
    result.push({label: '入口命令', value: taskParam?.entryPoint});
    // 如果 runtimeEnv 是数组，则将数组中的每个元素转换为 key:value 的形式（runtimeEnv 是为了兼容旧的版本）
    if (taskParam?.runtimeEnv && Array.isArray(taskParam?.runtimeEnv)) {
      result.push({
        label: '环境变量',
        value: taskParam?.runtimeEnv?.map((item: any) => `${item.name}:${item.value}`).join('，')
      });
    }
    // 如果 envVars 是数组，则将数组中的每个元素转换为 key:value 的形式
    if (taskParam?.envVars && Array.isArray(taskParam?.envVars)) {
      result.push({
        label: '环境变量',
        value: taskParam?.envVars?.map((item: any) => `${item.name}:${item.value}`).join('，')
      });
    }
    result.push({label: '运行集群', value: cluster?.clusterId});
    result.push({label: '集群类型', value: cluster?.clusterType === 'RESIDENT' ? '常驻' : '非常驻'});
    result.push({label: '引擎类型', value: cluster?.engineType});
    return result;
  }
  if (params.type === JobTaskType.DATAFLOW_TASK) {
    const taskParam = params.taskParam;
    const cluster = taskParam?.clusterList[0];
    const result = [];
    result.push({label: '并行度', value: taskParam?.parallel});
    result.push({label: '运行集群', value: cluster?.clusterId});
    result.push({label: '集群类型', value: cluster?.clusterType === 'RESIDENT' ? '常驻' : '非常驻'});
    result.push({label: '引擎类型', value: cluster?.engineType});
    return result;
  }
};

const operatorName = {
  databuilder_location_loader: '数据加载器',
  video_scanner: '视频目录扫描',
  column_filter: '过滤器',
  video_info_extractor: '视频信息提取器',
  video_scene_detector: '视频场景边界检测器',
  video_scene_cutter: '视频场景分割器',
  aesthetic_evaluation: '美学打分',
  optical_flow_evaluation: '光流打分',
  ocr_evaluation: '文字密集程度评价',
  pllava_caption: '视频生文字',
  matching_score: '匹配打分',
  exporter: '数据输出器'
};
const OperatorParams = {
  databuilder_location_loader: [
    {
      key: 'data_format',
      value: '数据输入类型'
    },
    {
      key: 'data_path',
      value: '数据输入路径'
    }
  ],
  video_scanner: [
    {
      key: 'video_bos_path_col',
      value: '视频地址列名'
    },
    {
      key: 'bos_ak_col',
      value: '视频获取AK',
      hidden: true
    },
    {
      key: 'bos_sk_col',
      value: '视频获取SK',
      hidden: true
    },
    {
      key: 'bos_endpoint_col',
      value: '视频获取Endpoint',
      hidden: true
    }
  ],
  column_filter: [
    {
      key: 'filter_col',
      value: '过滤列名'
    },
    {
      key: 'filter_operator',
      value: '过滤操作'
    },
    {
      key: 'filter_value',
      value: '过滤预置'
    }
  ],

  aesthetic_evaluation: [
    {
      key: 'aes_frames',
      value: '提取视频帧数的索引值'
    }
  ],
  pllava_caption: [
    {
      key: 'pooling_shape',
      value: '池化特征大小'
    },
    {
      key: 'num_frames',
      value: '处理视频采用帧数'
    },
    {
      key: 'use_lora',
      value: '使用lora(Low-Rank Adaptation) 微调技术'
    },
    {
      key: 'lora_alpha',
      value: 'LoRA的缩放参数'
    },
    {
      key: 'conv_mode',
      value: '对话模式'
    },
    {
      key: 'result_key',
      value: '视频理解结果输出的列名'
    }
  ],
  matching_score: [
    {
      key: 'match_key',
      value: '需要匹配打分列名'
    }
  ],
  exporter: [
    {
      key: 'data_format',
      value: '数据输出类型'
    },
    {
      key: 'export_path',
      value: '数据输出路径'
    }
  ]
};
// TODO 目前是前端写死的对应关系，后期需要后端返回展示
export const dealOperatorParams = (operate: any) => {
  const params = operate.params;
  const metaData = operate.metaData;

  const result = [];
  result.push({label: '节点ID', value: operate?.id});
  result.push({label: '算子名称', value: operatorName[operate?.metaData?.operatorName]});
  if (!Array.isArray(params)) {
    return result;
  }
  const map = new Map();
  params.forEach((item: any) => {
    map.set(item.key, item.value);
  });

  const bos_cut_video_bucket_name = map.get('bos_cut_video_bucket_name');
  const bos_cut_video_key = map.get('bos_cut_video_key');
  const cut_video_upload_volume = map.get('cut_video_upload_volume');
  // 视频场景分割器 数据输出路径 如果 bos_cut_video_bucket_name 和 bos_cut_video_key 存在，则展示数据输出路径
  if (metaData.operatorName === 'video_scene_cutter' && bos_cut_video_bucket_name && bos_cut_video_key) {
    result.push({label: '数据输出路径', value: `bos://${bos_cut_video_bucket_name}/${bos_cut_video_key}`});
  } else if (metaData.operatorName === 'video_scene_cutter' && cut_video_upload_volume) {
    // v2 版本
    result.push({label: '数据输出路径', value: cut_video_upload_volume});
  }

  if (!OperatorParams[metaData.operatorName]) {
    return result;
  }

  const operatorParams = OperatorParams[metaData.operatorName];
  operatorParams.forEach((item: any) => {
    if (item.hidden && !map.get(item.key)) {
      return;
    }
    result.push({label: item.value, value: map.get(item.key)});
  });

  return result;
};

export const dealOperatorResult = (operate: any, time: string) => {
  const result = [];

  const params = operate.params;
  const metaData = operate.metaData;

  if (!Array.isArray(params)) {
    return result;
  }
  let dataFormat = '';
  let exportPath = '';
  params?.forEach((item: any) => {
    switch (item.key) {
      case 'data_format':
      case '_side_output_data_format':
        dataFormat = item.value;
        break;
      case 'export_path':
      case '_side_output_export_path':
        exportPath = item.value;
        break;
    }
  });
  // 没有结果来源 或者 结果来源是数据加载器 不展示
  if (!dataFormat || metaData.operatorName === 'databuilder_location_loader') {
    return [];
  }

  result.push({label: '结果来源', value: operate.name});
  result.push({label: '结果类型', value: dataFormat});
  result.push({label: '生成时间', value: time});
  result.push({label: '路径', value: exportPath});

  return result;
};
