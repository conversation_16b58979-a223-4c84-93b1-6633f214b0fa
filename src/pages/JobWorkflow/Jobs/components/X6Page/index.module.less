.x6-main {
  position: relative;
  height: 100%;
  width: 100%;
  overflow: hidden;
  .x6-content {
    // 处理全局样式 body 覆盖 svg body 样式
    :global {
      body {
        min-width: 10px;
      }

      .task-node {
        overflow: hidden;
        height: 100%;
        font-size: 14px;
        text-align: center;
        color: #151b26;
        flex-direction: column;
        border-radius: 12px;
        background: #ffffff;
        box-shadow: 0 1px 4px 1px rgba(7, 12, 20, 0.12);
        filter: drop-shadow(0 1px 4px 1px rgba(7, 12, 20, 0.12));
        border: 1px solid #e8e9eb;
        .task-node-title {
          width: 100%;
          height: 52px;
          padding: 12px;
          font-size: 14px;
          font-weight: bold;
          text-align: left;
          color: #151b26;
          overflow: hidden;
          line-height: 20px;
          display: flex;
          &.dataflow-task {
            background-image: linear-gradient(180deg, #e7f0ff 0%, rgba(222, 227, 244, 0) 100%);
          }
          &.ray-task {
            background-image: linear-gradient(180deg, #dbdbdb 0%, rgba(255, 255, 255, 0) 100%);
          }
          .task-node-title-icon {
            flex: 0 0 32px;
          }
          .task-node-title-name {
            flex: 1 1 auto;
            overflow: hidden;
            line-height: 28px;
          }
        }
      }
      .operator-node {
        text-align: left;
        font-size: 12px;
        color: #151b26;
        border-radius: 8px;
        background: #f9f9fb;
        box-shadow: 0 1px 4px 1px rgba(0, 0, 0, 0.1);
        border: 1px solid #e8e9eb;
        padding: 6px 12px;
      }
    }
  }
  // 缩略图
  .minimap-container {
    position: absolute;
    bottom: 60px;
    right: 10px;

    width: 132px;
    height: 84px;
    z-index: 100;
    background-color: #fff;
    :global {
      .x6-widget-minimap {
        // border: 1px solid #ddd;
        border-radius: 8px;
        .x6-graph {
          box-shadow: none;
        }
        .x6-widget-minimap-viewport {
          box-shadow: 0 0 0 9999px rgba(7, 12, 20, 0.1);
          border: none;
        }

        .x6-widget-minimap-viewport-zoom {
          border: none;
          display: none;
        }
      }
    }
  }

  // 缩放按钮
  .zoom-btn {
    width: 132px;
    z-index: 100;
    position: absolute;
    bottom: 12px;
    right: 10px;
    background-color: #fff;
    :global {
      input {
        text-align: center;
      }
    }
  }
}
