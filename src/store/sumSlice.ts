import {createSlice} from '@reduxjs/toolkit';

export interface ICounterState {
  value: number;
}

const initialState: ICounterState = {
  value: 0
};

const counterSlice = createSlice({
  name: 'counter',
  initialState,
  reducers: {
    incremented: (state: ICounterState) => {
      state.value += 1;
    },
    decremented: (state: ICounterState) => {
      state.value -= 1;
    }
  }
});

// 导出方法
export const {incremented, decremented} = counterSlice.actions;

// 默认导出
export default counterSlice.reducer;
