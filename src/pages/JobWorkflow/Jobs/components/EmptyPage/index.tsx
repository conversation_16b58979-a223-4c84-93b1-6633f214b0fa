import React, {useEffect, useState} from 'react';
import styles from './index.module.less';
import {Button} from 'acud';
import createEmptyPng from '@assets/png/workflow/create_empty.png';
import createJsonPng from '@assets/png/workflow/create_json.png';
import {JobCreateType} from '@pages/JobWorkflow/constants';

const JobEmptyPage: React.FC<{
  setJobModal: (modal: {isVisible: boolean; jobType?: JobCreateType}) => void;
}> = ({setJobModal}) => {
  return (
    <div className={styles['empty-page']}>
      <div className={styles['content']}>
        <img className={styles['img']} src={createEmptyPng}></img>
        <div className={styles['title']}>创建空白工作流</div>
        <div className={styles['desc']}>自定义Json编辑创建 </div>
        <Button
          className={styles['btn']}
          type="primary"
          onClick={() => {
            setJobModal({isVisible: true, jobType: JobCreateType.EMPTY});
          }}
        >
          立即创建
        </Button>
      </div>

      <div className={styles['content']}>
        <img className={styles['img']} src={createJsonPng}></img>
        <div className={styles['title']}>导入工作流</div>
        <div className={styles['desc']}>预置模版或者导入文件快速创建 </div>
        <Button
          className={styles['btn']}
          type="primary"
          onClick={() => {
            setJobModal({isVisible: true, jobType: JobCreateType.JSON});
          }}
        >
          立即导入
        </Button>
      </div>
    </div>
  );
};

export default JobEmptyPage;
