/**
 * ShowUploadFile 组件
 *
 * 该组件接收一个文件上传列表 `uploadList`，并根据文件的上传状态（加载中、成功、失败）显示相应的进度条和操作按钮。
 * 用户可以通过点击按钮重新上传失败的文件，或者折叠/展开文件列表，关闭整个上传组件。
 * <AUTHOR>
 */

import React, {forwardRef, useEffect, useImperativeHandle, useMemo, useState} from 'react';
import {Alert, Button, Progress, Tooltip} from 'acud';
import {ProgressStatus} from 'acud/lib/progress/interface';
import {RcFile} from 'acud/lib/upload';
import {Close1, CollapseDown, CollapseUp} from '@baidu/xicon-react-bigdata';

import {formatBytes} from '@utils/utils';
import TextEllipsis from '@components/TextEllipsisTooltip';

import './index.less';
import IconSvg from '@components/IconSvg';
import {getIconTypeFromName} from '@pages/MetaData/helper';

export type IfileInfo = {
  file: RcFile; // https://developer.mozilla.org/docs/Web/API/File
  status: 'loading' | 'success' | 'error'; // 当前文件上传状态
  [key: string]: any; // 可以往这个对象上塞其他属性，在 reUploadRequest 方法实现中可能用到
};

export interface ShowUploadFileRefHandle {
  expand: () => void;
  close: () => void;
}

export interface IShowUploadFileProps {
  /**
   * 文件上传 List 队列
   */
  uploadList: Array<IfileInfo>;
  /**
   * 更新文件 List 项内容 方法
   */
  setUploadListFun: React.Dispatch<React.SetStateAction<IShowUploadFileProps['uploadList'] | []>>;
  /**
   * 重新上传方法
   * @param file 当前文件信息
   * @returns
   */
  reUploadRequest: (file: any) => void;
  afterCloseFn?: () => void;
}

const ShowUploadFile = (props: IShowUploadFileProps, ref) => {
  const {uploadList = [], setUploadListFun, reUploadRequest, afterCloseFn} = props;

  // 是否展开列表，默认展开
  const [isExpand, setIsExpand] = useState(true);
  // 是否展示组件，默认展示
  const [visible, setVisible] = useState(false);

  useImperativeHandle(ref, () => ({
    expand: () => {
      setIsExpand(true);
      setVisible(true);
    },
    close: handleClose
  }));

  const list = useMemo(() => {
    return uploadList.map((item) => {
      let percent = item.percent || 0;
      let status: ProgressStatus = 'active';
      if (item.status === 'loading') {
        percent = item.percent;
      } else if (item.status === 'success') {
        percent = 100;
        status = 'success';
      } else {
        percent = 100;
        status = 'error';
      }
      return {
        ...item,
        percent,
        status
      };
    });
  }, [uploadList]);

  // 关闭组件，重置 uploadList
  const handleClose = () => {
    setVisible(false);
    setUploadListFun([]);
    afterCloseFn?.();
  };

  if (!visible) {
    return null;
  }

  const uploadingNum = list.filter((item) => item.status === 'success').length;

  const percent = Math.floor((uploadingNum / list.length) * 100);

  const status =
    uploadingNum === list.length
      ? list.filter((item) => item.status === 'success').length === list.length
        ? 'success'
        : 'error'
      : 'active';

  return (
    <div className="show-uploading-file-com">
      <header>
        <h4>
          {uploadingNum === list.length ? '完成' : '正在'}上传
          {uploadingNum}/{list.length} 个文件
        </h4>
        <div style={{display: 'flex', alignItems: 'center', gap: '8px'}}>
          {/* 折叠按钮 */}
          {isExpand ? (
            <span onClick={() => setIsExpand(false)}>
              <CollapseDown
                theme="line"
                color="#6c6d70"
                size={16}
                strokeWidth={2}
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </span>
          ) : (
            <span onClick={() => setIsExpand(true)}>
              <CollapseUp
                theme="line"
                color="#6c6d70"
                size={16}
                strokeWidth={2}
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </span>
          )}
          {/* 关闭按钮 */}
          <span onClick={handleClose}>
            <Close1
              theme="line"
              color="#6c6d70"
              size={16}
              strokeWidth={2}
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </span>
        </div>
      </header>

      {/* 上传文件列表 */}
      {isExpand && (
        <>
          <Alert
            showIcon={false}
            message="刷新或关闭浏览器会取消当前上传任务，并清除全部记录"
            banner
            type="info"
            closable
            className="show-uploading-file-com-alert"
          />
          <div className="show-uploading-file-com-list">
            {list.map((item) => {
              const [iconType, color] = getIconTypeFromName(item.file.name);
              return (
                <div key={item.file.uid} className="upload-item">
                  <div className="upload-item-header">
                    <span className="upload-item-header-name">
                      <IconSvg className="upload-item-header-icon" type={iconType} size={16} color={color} />
                      <TextEllipsis tooltip={item.file.name} width={260}>
                        {item.file.name}
                      </TextEllipsis>
                    </span>
                    <div>
                      <span
                        style={{
                          marginLeft: '8px',
                          color: '#888',
                          marginRight: item.status === 'error' ? '20px' : '36px'
                        }}
                      >
                        {formatBytes(item.file.size)}
                      </span>
                      {item.status === 'error' && (
                        <Button
                          className="upload-item-header-btn"
                          type="actiontext"
                          onClick={() => reUploadRequest({originFileObj: item.file})}
                        >
                          <IconSvg type="refresh-2-arrow" size={16} color="#6c6d70" />
                        </Button>
                      )}
                    </div>
                  </div>
                  <Progress
                    size="small"
                    key={item.file.uid}
                    percent={item.percent}
                    status={item.status}
                    infoType="none"
                  />
                </div>
              );
            })}
          </div>
        </>
      )}
      {!isExpand && (
        <Progress
          className="show-uploading-file-com-progress"
          size="small"
          percent={percent}
          status={status}
          infoType="none"
        />
      )}
    </div>
  );
};

export default forwardRef(ShowUploadFile);
