// 任务实例的状态
export enum JobInstanceStatus {
  /**
   * The instance is initializing.
   */
  INIT = 'INIT',

  /**
   * The instance is actively running.
   */
  RUNNING_EXECUTION = 'RUNNING_EXECUTION',

  /**
   * We have called "pause" on the instance, but it has not yet transitioned to fully paused.
   */
  READY_PAUSE = 'READY_PAUSE',

  /**
   * The instance is fully paused.
   */
  PAUSE = 'PAUSE',

  /**
   * We have called "stop" on the instance, but it has not yet transitioned to fully stopped.
   */
  READY_STOP = 'READY_STOP',

  /**
   * The instance has been completely stopped.
   */
  STOP = 'STOP',

  /**
   * The instance has failed.
   */
  FAILURE = 'FAILURE',
  // 提交失败 一般是 json 配置有问题
  SUBMIT_FAILURE = 'SUBMIT_FAILURE',

  /**
   * The instance completed successfully.
   */
  SUCCESS = 'SUCCESS'
}

// 任务实例的操作类型
export enum JobInstanceOperateType {
  PAUSE = 'pause',
  STOP = 'stop',
  RESUME = 'resume',
  RERUN = 'rerun'
}

/** 重新运行类型 */
export enum JobInstanceRerunType {
  // 对应失败点继续
  FROM_FAILURE = 'FROM_FAILURE',
  // 对应从头开始重跑
  FROM_FIRST = 'FROM_FIRST'
}

// 任务实例的 task 状态
export enum JobInstanceTaskStatus {
  /**
   * Task that waiting for be scheduled.
   */
  PENDING = 'PENDING',
  /**
   * The instance is actively running.
   */
  RUNNING_EXECUTION = 'RUNNING_EXECUTION',
  /**
   * The instance is fully paused.
   */
  PAUSE = 'PAUSE',
  /**
   * The instance stop and the running task will be killed.
   */
  KILL = 'KILL',
  /**
   * The instance has failed.
   */
  FAILURE = 'FAILURE',
  /**
   * The instance completed successfully.
   */
  SUCCESS = 'SUCCESS',
  /**
   * All
   */
  ALL = 'ALL'
}

// 任务实例 task 状态
export const JobInstanceTaskStatusMap = {
  [JobInstanceTaskStatus.PENDING]: {
    label: '等待中',
    color: 'default'
  },
  [JobInstanceTaskStatus.RUNNING_EXECUTION]: {
    label: '运行中',
    color: 'active'
  },
  [JobInstanceTaskStatus.PAUSE]: {
    label: '暂停',
    color: 'warning'
  },
  [JobInstanceTaskStatus.KILL]: {
    label: '已终止',
    color: 'inactive'
  },
  [JobInstanceTaskStatus.FAILURE]: {
    label: '失败',
    color: 'error'
  },
  [JobInstanceTaskStatus.SUCCESS]: {
    label: '成功',
    color: 'success'
  }
};
