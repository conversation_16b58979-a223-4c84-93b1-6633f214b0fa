import {createJob} from '@api/job';
import {ITemplate, queryTemplateList} from '@api/template';
import {Button, Form, Input, Modal, Select, Space, toast, Upload} from 'acud';
import {OutlinedButtonUpload} from 'acud-icon';
import {useMemoizedFn} from 'ahooks';
import React, {useContext, useEffect, useState} from 'react';
import styles from './index.module.less';
import {RULE} from '@utils/regs';
import {WorkspaceContext} from '@pages/index';
import {JobCreateType} from '@pages/JobWorkflow/constants';
import urls from '@utils/urls';
import {useNavigate} from 'react-router-dom';

/** 创建报表弹窗属性 */
export interface ICreateJobModalProps {
  isVisible: boolean;
  // 区分新建任务类型 空任务 或者 导入 任务
  jobType?: JobCreateType;
  // 确定按钮回调函数 flag 控制是否刷新页面
  onSubmit: (flag: boolean) => void;
}

const JobCreateModal: React.FC<ICreateJobModalProps> = (props: ICreateJobModalProps) => {
  const {isVisible, jobType, onSubmit} = props;
  const {workspaceId} = useContext(WorkspaceContext);
  const navigate = useNavigate();
  const [form] = Form.useForm();
  // 查询字段
  const [templateArr, setTemplateArr] = useState<ITemplate[]>([]);
  const [loading, setLoading] = useState(false);
  // 文件上传字段 控制文件上传后显示文件名和禁用按钮
  const [fileJson, setFileJson] = useState<{
    name?: string;
    json?: string;
    disabled: boolean;
  } | null>(null);

  // 提交表单
  const handleOk = useMemoizedFn(async () => {
    setLoading(true);
    try {
      const obj = await form.validateFields();
      const {result, success} = await createJob(obj, workspaceId);
      if (success) {
        // 导航到编辑页面
        navigate(`${urls.jobDetail}?jobId=${result}&edit=true`);
      }
    } catch (error) {
      console.error(error);
    }
    setLoading(false);
  });
  // 关闭弹窗
  const handleCancel = useMemoizedFn(() => {
    onSubmit(false);
  });

  // 查询模板列表
  const queryList = async () => {
    const {result} = await queryTemplateList(workspaceId);
    setTemplateArr(result.result);
  };

  // 初始化表单
  const initFn = useMemoizedFn(() => {
    //  JSON 类型需要使用模板列表
    jobType === JobCreateType.JSON && queryList();
    form.resetFields();
    setFileJson(null);
    setLoading(false);
  });

  useEffect(() => {
    // 打开弹窗 初始化表单
    if (isVisible) {
      initFn();
    }
  }, [isVisible]);

  // 上传文件前检查
  const beforeUpload = useMemoizedFn((file: File) => {
    const reader = new FileReader();

    reader.addEventListener(
      'load',
      () => {
        let json = reader.result!.toString();
        try {
          json = JSON.parse(json); // 尝试解析 JSON
        } catch {
          toast.error({
            message: '文件格式错误，请检查json文件',
            duration: 5
          });
          return;
        }
        // 格式化 json
        json = JSON.stringify(json, null, 2);
        // 然后这将显示一个文本文件
        setFileJson({
          name: file.name,
          json,
          disabled: false
        });

        form.setFieldValue('code', json);
        form.validateFields(['code']);
      },
      false
    );
    reader.readAsText(file);

    return Upload.LIST_IGNORE;
  });

  // 切换模板
  const changeTemplate = useMemoizedFn((value: string) => {
    setFileJson({disabled: !!value});
  });

  return (
    <Modal
      closable={true}
      width={500}
      title={jobType === JobCreateType.EMPTY ? '新建空白工作流' : '导入工作流'}
      visible={isVisible}
      onOk={handleOk}
      confirmLoading={loading}
      onCancel={handleCancel}
      className={styles['job-create-modal']}
    >
      <Form name="basic" layout="vertical" labelAlign="left" form={form} inputMaxWidth="500px">
        <Form.Item
          label="工作流名称"
          name="name"
          rules={[
            {required: true, message: '请输入工作流名称'},
            {pattern: RULE.workflowName, message: RULE.workflowNameText}
          ]}
        >
          <Input placeholder={RULE.workflowNameText} allowClear forbidIfLimit={true} limitLength={256} />
        </Form.Item>

        <Form.Item label="描述" name="description">
          <Input.TextArea forbidIfLimit={true} limitLength={500} placeholder="请输入空间描述" allowClear />
        </Form.Item>

        {jobType === JobCreateType.JSON && (
          <div className={styles['template-upload']}>
            <Form.Item
              className={styles['template-select']}
              label="选择模板"
              name="code"
              rules={[
                {
                  required: true,
                  message: '请选择模板或上传文件'
                }
              ]}
              extra="可以从模板中导入，也可以自定义上传，支持json格式"
            >
              <Select
                className="w-full"
                allowClear
                placeholder="请选择模板"
                options={[...templateArr, {templateName: fileJson?.name, jobTemplate: fileJson?.json}]
                  .filter((item) => item.templateName!)
                  .map((item) => {
                    return {
                      label: String(item.templateName),
                      value: item.jobTemplate!
                    };
                  })}
                onChange={changeTemplate}
              ></Select>
            </Form.Item>

            <Form.Item label=" " className={styles['upload-btn']}>
              <Upload accept="application/json" desPosition="right" beforeUpload={beforeUpload}>
                <Button
                  className={styles['upload-btn']}
                  disabled={fileJson?.disabled}
                  icon={<OutlinedButtonUpload />}
                >
                  上传文件
                </Button>
              </Upload>
            </Form.Item>
          </div>
        )}
      </Form>
    </Modal>
  );
};

export default JobCreateModal;
