import React, {forwardRef, useImperativeHandle, useState} from 'react';
import {Modal, Input, Form} from 'acud';
import {createWorkspaceFolder, GetWorkspaceFolderListResult} from '@api/WorkArea';
import {useRequest} from 'ahooks';

import {FOLDER_LIMIT_LENGTH, FOLDER_NAME_ERROR_MESSAGE, FOLDER_NAME_REGEX} from '@pages/WorkArea/config';

interface CreateFolderModalProps {
  onSuccess?: (record: GetWorkspaceFolderListResult) => void;
  parentId?: string;
  workspaceId: string;
}

export interface CreateFolderModalRef {
  open: (record: GetWorkspaceFolderListResult) => void;
  close: () => void;
}

const CreateFolderModal = forwardRef<CreateFolderModalRef, CreateFolderModalProps>((props, ref) => {
  const [visible, setVisible] = useState(false);
  const [record, setRecord] = useState<GetWorkspaceFolderListResult | null>(null);

  const [form] = Form.useForm();

  const {loading, run: createFolder} = useRequest(createWorkspaceFolder, {
    manual: true,
    onSuccess: (res) => {
      if (res.success && res.result) {
        props.onSuccess?.(res.result);
        setVisible(false);
      }
    },
    onError: (error) => {
      console.error('创建文件夹失败:', error);
    }
  });

  useImperativeHandle(ref, () => ({
    open: (record: GetWorkspaceFolderListResult) => {
      setVisible(true);
      setRecord(record);
      form.setFieldsValue({name: ''});
    },
    close: () => setVisible(false)
  }));

  const handleConfirm = async () => {
    await form.validateFields();
    const name = form.getFieldValue('name');
    await createFolder({
      name,
      workspaceId: props.workspaceId,
      parentId: record?.id || ''
    });
  };

  const nameRules = [
    {required: true, message: '请输入名称'},
    {
      validator: (_, value) => {
        if (!FOLDER_NAME_REGEX.test(value)) {
          return Promise.reject(new Error(FOLDER_NAME_ERROR_MESSAGE));
        }
        return Promise.resolve();
      }
    }
  ];

  return (
    <Modal
      title="创建文件夹"
      visible={visible}
      onOk={handleConfirm}
      onCancel={() => setVisible(false)}
      confirmLoading={loading}
      destroyOnClose
    >
      <Form form={form} layout="vertical" inputMaxWidth="100%">
        <Form.Item name="name" label="文件夹名称" rules={nameRules} extra={FOLDER_NAME_ERROR_MESSAGE}>
          <Input limitLength={FOLDER_LIMIT_LENGTH} forbidIfLimit placeholder="请输入名称" />
        </Form.Item>
      </Form>
    </Modal>
  );
});

CreateFolderModal.displayName = 'CreateFolderModal';

export default CreateFolderModal;
