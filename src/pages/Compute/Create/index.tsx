/**
 * 创建计算资源组件
 * 主要功能：
 *  1. 获取 VPC 列表
 *  2. 渲染 VPC 选择器
 *  3. 选择 VPC 后，获取该 VPC 下的子网列表
 *  4. 渲染子网选择器
 *  5. 创建计算资源
 * 主要逻辑：
 *  1. 使用 useRequest 获取 VPC 列表
 *  2. 使用 useEffect 监听 VPC 选择器的变化，获取子网列表
 *  3. 使用 useRequest 创建计算资源
 * <AUTHOR>
 */
import React, {useEffect, useState, useContext, useCallback, useMemo} from 'react';
import _ from 'lodash';
import {useRegion} from '@hooks/useRegion';
import {Button, Form, Input, Select, InputNumber, Checkbox, toast, Radio} from 'acud';
import {createComputeResource, getZoneList} from '@api/Compute';
import {WorkspaceContext} from '@pages/index';
import SubnetSelect from './SubnetSelect';
import VpcSelect from './VpcSelect';
import {NAME_LIMIT_LENGTH, NAME_REGEX, NAME_ERROR_MESSAGE, PAY_TYPE} from '../config';
import {useNavigate} from 'react-router-dom';
import urls from '@utils/urls';
import {Zone} from '@api/Compute';
import {useRequest} from 'ahooks';
import styles from './index.module.less';
import classNames from 'classnames/bind';
import PaySelect from './PaySelect';
import IconSvg from '@components/IconSvg';

const cx = classNames.bind(styles);

export interface ZoneOption {
  value: string;
  label: string;
  disabled?: boolean;
  specs: Zone['specs'];
}

const payTypeOptions = PAY_TYPE.toArray('POSTPAID', 'PREPAID').map((item) => ({
  ...item,
  value: item.value,
  label: item.text,
  disabled: item.value === PAY_TYPE.PREPAID
}));

const rayVersion = 'V1.0 (Ray2.35.0, Python 3.9.19)';

const ComputeResourceCreate: React.FC = () => {
  const [form] = Form.useForm();
  const {currentRegion} = useRegion();
  const {workspaceId} = useContext(WorkspaceContext);
  const [vpcId, setVpcId] = useState<string | undefined>();
  const [zone, setZone] = useState<string | undefined>();
  const [zoneList, setZoneList] = useState<ZoneOption[]>([]);
  const [clusterTypeOptions, setClusterTypeOptions] = useState([]);
  const [nodeType, setNodeType] = useState('CPU');
  const [serviceAgreement, setServiceAgreement] = useState(false);
  const [clusterType, setClusterType] = useState('');
  const [nodeCnt, setNodeCnt] = useState(2);

  const navigate = useNavigate();

  const initialValues = {
    workspaceId,
    chargingType: 'Postpaid',
    region: currentRegion.id,
    engine: 'Ray',
    mirrorVersion: 'v1',
    nodeType: 'CPU',
    clusterType: '',
    nodeCnt: 2
  };

  const {loading: zoneLoading, run: getZoneListRun} = useRequest(getZoneList, {
    onSuccess: (res) => {
      if (!res.success) {
        return;
      }
      const zoneList = _.map<Zone, ZoneOption>(res.result.zones, (zone) => {
        const disabled = _.reduce(zone.specs, (acc, spec) => acc && spec.inventoryQuantity === 0, true);

        const zoneName = zone.zoneName?.replace?.(
          /zone([A-Z])/g,
          (_, c) => `可用区${c} ${disabled ? '(已售罄)' : ''}`
        );

        return {
          specs: zone.specs,
          value: zone.zoneName,
          label: zoneName,
          disabled
        };
      });
      setZoneList(zoneList);
    }
  });

  useEffect(() => {
    getZoneListRun();
  }, [getZoneListRun]);

  const onVpcChange = (vpcId?: string) => {
    setVpcId(vpcId);
    form.setFieldsValue({
      clusterType: '',
      subnetId: ''
    });
  };

  const onZoneChange = (value: string) => {
    setZone(value);
    form.setFieldsValue({
      clusterType: ''
    });
    const zone = zoneList.find((item) => item.value === value);
    if (zone) {
      const types = _.map(zone.specs, (item) => {
        const disabled = item.inventoryQuantity === 0;
        return {
          value: item.dataBuilderClusterType,
          label: `${item.dataBuilderClusterType} ${disabled ? '(已售罄)' : ''}`,
          type: item.type,
          disabled
        };
      });
      setClusterTypeOptions(types);
    }
  };

  useEffect(() => {
    const validZone = zoneList.find((item) => !item.disabled);
    if (validZone) {
      setZone(validZone.value);
      form.setFieldsValue({
        availableZone: validZone.value
      });
      onZoneChange(validZone.value);
    }
  }, [zoneList]);

  const nameRules = [
    {required: true, message: '请输入名称'},
    {
      validator: (_, value) => {
        if (!NAME_REGEX.test(value)) {
          return Promise.reject(new Error(NAME_ERROR_MESSAGE));
        }
        return Promise.resolve();
      }
    }
  ];

  const vpcRules = [{required: true, message: '请选择网络'}];
  const zoneRules = [{required: true, message: '请选择可用区'}];
  const subnetRules = [{required: true, message: '请选择子网'}];
  const clusterTypeRules = [{required: true, message: '请选择节点规格'}];

  const [loading, setLoading] = useState(false);
  const handleSubmit = async () => {
    if (loading) {
      return;
    }
    try {
      setLoading(true);
      await form.validateFields();
      if (!serviceAgreement) {
        toast.error({
          message: '请先阅读并同意服务协议信息',
          duration: 3
        });
        return;
      }
      const values = await form.getFieldsValue();
      console.log('values', values);
      const res = await createComputeResource({
        ...values,
        workspaceId
      });
      console.log('res', res);
      if (res.success) {
        toast.success({
          message: '创建成功',
          duration: 3
        });
        goComputeList();
      }
    } catch (error) {
      console.error('失败:', error);
    } finally {
      setLoading(false);
    }
  };

  function goComputeList() {
    navigate(`${urls.compute}?workspaceId=${workspaceId}`);
  }

  const formValues = form.getFieldsValue();

  const zoneName = useMemo(
    () => _.find(zoneList, (item) => item.value === zone)?.label || '',
    [zoneList, zone]
  );

  const onNodeTypeChange = (e) => {
    setNodeType(e.target.value);
    form.setFieldsValue({
      clusterType: ''
    });
  };

  const filteredClusterTypeOptions = useMemo(() => {
    return clusterTypeOptions.filter((option) => option.type === nodeType);
  }, [clusterTypeOptions, nodeType]);

  const onNodeCntChange = (value: number) => {
    setNodeCnt(value);
  };

  const onClusterTypeChange = (value: string) => {
    setClusterType(value);
  };

  const clusterTypeName = useMemo(() => {
    const item = _.find(clusterTypeOptions, (item) => item.value === clusterType);
    return item?.label || '';
  }, [clusterTypeOptions, clusterType]);

  return (
    <main className={cx('compute-wrapper')}>
      <div className={cx('compute-content')}>
        <div className={cx('compute-title')}>
          <IconSvg
            type="right"
            size={18}
            color="#5C5F66"
            className={cx('compute-title-icon')}
            onClick={() => {
              goComputeList();
            }}
          />
          创建常驻集群
        </div>
        <div className={cx('form-wrapper')}>
          <Form
            form={form}
            initialValues={initialValues}
            labelAlign="left"
            inputMaxWidth="480px"
            labelWidth="94px"
            colon={false}
          >
            <div className={styles['legend-title']}>付费及地域</div>
            <Form.Item name="chargingType" label="付费方式" required>
              <PaySelect options={payTypeOptions as any[]}></PaySelect>
            </Form.Item>
            <Form.Item className={cx('margin-bottom-40px')} name="region" label="地域" required>
              <span>{currentRegion.label}</span>
            </Form.Item>
            <div className={styles['legend-title']}>网络及可用区</div>
            <Form.Item name="vpcId" label="网络" rules={vpcRules}>
              <VpcSelect onChange={onVpcChange}></VpcSelect>
            </Form.Item>
            <Form.Item className={cx('margin-bottom-40px')} label="可用区与子网" required>
              <div className={cx('combine-zone-subnet', 'flex')}>
                <Form.Item className={cx('zone-select')} name="availableZone" rules={zoneRules}>
                  <Select loading={zoneLoading} options={zoneList} onChange={onZoneChange}></Select>
                </Form.Item>
                <Form.Item className="flex-1" name="subnetId" rules={subnetRules}>
                  <SubnetSelect vpcId={vpcId} zone={zone}></SubnetSelect>
                </Form.Item>
              </div>
              <div className="acud-form-item-extra">
                如需创建新的子网，您可以到
                <a href="/network/#/vpc/subnet/list" target="_blank" rel="noreferrer">
                  私有网络-子网
                </a>
                创建
              </div>
            </Form.Item>
            <div className={styles['legend-title']}>集群信息</div>
            <Form.Item name="name" extra={NAME_ERROR_MESSAGE} label="集群名称" rules={nameRules}>
              <Input limitLength={NAME_LIMIT_LENGTH} forbidIfLimit></Input>
            </Form.Item>
            <Form.Item name="engine" label="集群引擎">
              <span>Ray</span>
            </Form.Item>
            <Form.Item className={cx('margin-bottom-40px')} name="mirrorVersion" label="镜像版本">
              <span>{rayVersion}</span>
            </Form.Item>
            <div className={styles['legend-title']}>节点配置</div>
            <Form.Item name="nodeType" label="节点类型">
              <Radio.Group
                options={[
                  {value: 'CPU', label: 'CPU'},
                  {value: 'GPU', label: 'GPU'}
                ]}
                onChange={onNodeTypeChange}
              ></Radio.Group>
            </Form.Item>
            <Form.Item name="clusterType" label="节点规格" rules={clusterTypeRules}>
              <Select
                className="w-full"
                options={filteredClusterTypeOptions}
                onChange={onClusterTypeChange}
              ></Select>
            </Form.Item>
            <Form.Item name="nodeCnt" label="购买数量" required>
              <InputNumber min={1} max={50} symmetryMode onChange={onNodeCntChange} />
            </Form.Item>
          </Form>
        </div>

        <div className={cx('order-info')}>
          <div className={cx('order-info-title')}>千帆DataBuilder 订单明细</div>
          <div className={cx('order-info-content')}>
            <div className={cx('order-info-content-item')}>
              <span className={cx('order-info-content-item-label')}>付费方式</span>
              <span className={cx('order-info-content-item-value')}>
                {PAY_TYPE.getTextFromValue(formValues.chargingType)}
              </span>
            </div>
            <div className={cx('order-info-content-item')}>
              <span className={cx('order-info-content-item-label')}>地域</span>
              <span className={cx('order-info-content-item-value')}>{currentRegion.label || '-'}</span>
            </div>
            <div className={cx('order-info-content-item')}>
              <span className={cx('order-info-content-item-label')}>引擎类型</span>
              <span className={cx('order-info-content-item-value')}>Ray</span>
            </div>
            <div className={cx('order-info-content-item')}>
              <span className={cx('order-info-content-item-label')}>版本</span>
              <span className={cx('order-info-content-item-value')}>{rayVersion}</span>
            </div>
            <div className={cx('order-info-content-item')}>
              <span className={cx('order-info-content-item-label')}>可用区</span>
              <span className={cx('order-info-content-item-value')}>{zoneName || '-'}</span>
            </div>
            <div className={cx('order-info-content-item')}>
              <span className={cx('order-info-content-item-label')}>节点规格</span>
              <span className={cx('order-info-content-item-value')}>{clusterTypeName || '-'}</span>
            </div>
            <div className={cx('order-info-content-item')}>
              <span className={cx('order-info-content-item-label')}>节点总数</span>
              <span className={cx('order-info-content-item-value')}>{nodeCnt}</span>
            </div>
          </div>
          <div className={cx('order-info-price')}>
            <div className={cx('order-info-price-title')}>费用</div>
            <div className={cx('order-info-price-value')}>
              <span className={cx('symbol')}>￥</span>0
            </div>
          </div>
          <div className={cx('order-info-price-service-agreement')}>
            <Checkbox checked={serviceAgreement} onChange={(e) => setServiceAgreement(e.target.checked)}>
              <>
                我已阅读并同意
                <a
                  href="https://console.bce.baidu.com/iam/agreement-v2.html"
                  target="_blank"
                  rel="noreferrer"
                >
                  《百度智能云线上订购协议》
                </a>
              </>
            </Checkbox>
          </div>
          <Button className={cx('order-info-button')} type="primary" loading={loading} onClick={handleSubmit}>
            立即创建
          </Button>
        </div>
      </div>
    </main>
  );
};

export default ComputeResourceCreate;
