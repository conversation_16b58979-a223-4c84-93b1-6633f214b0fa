/**
 * VPC选择组件
 * 该组件主要提供了选择VPC的功能
 * <AUTHOR>
 */
import React, {useEffect, useState} from 'react';
import {Select} from 'acud';
import {getVpcList} from '@api/Compute';
import _ from 'lodash';
import {useRequest} from 'ahooks';
import {LabeledValue} from 'acud/es/select';
import RefreshButton from '@components/RefreshButton';

interface VpcSelectProps {
  value?: string;
  onChange?: (value: string | undefined) => void;
}

const VpcSelect: React.FC<VpcSelectProps> = ({value, onChange}) => {
  const [selectedVpc, setSelectedVpc] = useState(value);

  useEffect(() => {
    setSelectedVpc(value);
  }, [value]);

  useEffect(() => {
    if (selectedVpc !== undefined) {
      onChange?.(selectedVpc);
    }
  }, [selectedVpc]);

  // vpc下拉列表
  const [vpcOptions, setVpcOptions] = useState<LabeledValue[]>([]);
  const {loading, run: getVpcOptions} = useRequest(
    async () => {
      const {result} = await getVpcList();
      const options = _.map(result, (item) => ({
        value: item.vpcId,
        label: `${item.name} | ${item.cidr}`
      }));
      setVpcOptions(options);
      const defaultVpc = _.find(result, (item) => item.defaultVpc);
      if (defaultVpc) {
        setSelectedVpc(defaultVpc.vpcId);
      }
    },
    {
      manual: true
    }
  );

  useEffect(() => {
    getVpcOptions();
  }, [getVpcOptions]);

  const vpcExtra = (
    <div>
      如需创建新的私有网络，您可以到
      <a href="/network/#/vpc/instance/list" target="_blank" rel="noreferrer">
        私有网络
      </a>
      创建
    </div>
  );

  return (
    <>
      <div className="flex items-center">
        <Select
          className="flex-1"
          loading={loading}
          value={selectedVpc}
          options={vpcOptions}
          placeholder="请选择VPC"
          onChange={(value) => {
            setSelectedVpc(value);
          }}
        />
        <RefreshButton className="ml-[8px]" onClick={getVpcOptions} />
      </div>
      <div className="acud-form-item-extra">{vpcExtra}</div>
    </>
  );
};

export default VpcSelect;
