import {FC, useMemo} from 'react';
import {Menu, Select, Dropdown, Link} from 'acud';
import {queryWorkspaceList} from '@api/workspace';
import {useNavigate} from 'react-router-dom';
import urls from '@utils/urls';
import {useRegion} from '@hooks/useRegion';
import {useCookieState, useRequest} from 'ahooks';
import useUrlState from '@ahooksjs/use-url-state';
import cx from 'classnames';
import AvatarDropdown from './AvatarDropdown';
import store from '@store/index';

import styles from './index.module.less';
import IconSvg from '@components/IconSvg';

interface IWorkspaceHeaderProps {
  workspaceId: string;
}

// TODO：修改文档链接
// 最好不要用enum 如果用enum 国际化无法处理
const infoList = [
  {
    name: '财务',
    icon: 'header-billing',
    children: [
      {
        label: '新建工单',
        key: 'https://console.bce.baidu.com/support/#/ticket/#/ticket/create'
      },
      {
        label: '工单列表',
        key: 'https://console.bce.baidu.com/support/#/ticket/#/ticket/list'
      }
    ]
  }
  // {
  //   name: '文档',
  //   icon: 'header-doc',
  //   children: [
  //     {
  //       label: '产品文档',
  //       key: 'https://console.bce.baidu.com/support/#/ticket/#/ticket/list'
  //     }
  //   ]
  // }
];

// 添加logo
// const logoInfo = {
//   logo: <img src={logoSrc} width={150} />,
//   urlConfig: {
//     href: '/databuilder/',
//     target: '_self'
//   }
// };

const RightMenu = ({items}: {items: Array<{label: string; key: string}>}) => (
  <Menu
    onClick={({key}: {key: string}) => {
      window.open(key, '_blank');
    }}
  >
    {items.map((item) => (
      <Menu.Item key={item.key}>{item.label}</Menu.Item>
    ))}
  </Menu>
);

const Header: FC<IWorkspaceHeaderProps> = ({workspaceId}) => {
  const {currentRegion} = useRegion();
  const [, setUrlParams] = useUrlState();

  // 获取用户名 framework也是这么拿的
  const [userName] = useCookieState('bce-login-display-name');
  const navigate = useNavigate();

  const {data, loading} = useRequest(queryWorkspaceList, {
    defaultParams: [{pageSize: 10000}],
    onSuccess: (res) => {
      // TODO: 修改主页url
      // 如果当前workspaceId不在workspaceList中，则跳转 或跳无权限？？后续再定
      if (!res.result.items.find((item) => item.id === workspaceId)) {
        // navigate(urls.);  FIXME: 跳转到 ？
      }
    }
  });

  const workspaceList = useMemo(() => {
    return data?.result.items.map((item) => ({
      label: item.name,
      value: item.id,
      disabled: item.role === 'UNKNOWN' || item.status === 'ERROR'
    }));
  }, [data]);

  // 工作空间选择器
  const WorkspaceSelect = useMemo(
    () => (
      <Select
        options={workspaceList}
        loading={loading}
        value={workspaceId}
        showSearch
        filterOption={(inputValue, option) => (option.label as string).includes(inputValue)}
        onChange={(value) => {
          setUrlParams((pre) => ({
            ...Object.keys(pre || {}).reduce((res, item) => {
              res[item] = undefined;
              return res;
            }, {}),
            workspaceId: value
          }));
          // 重置工作空间权限
          store.dispatch({
            type: 'workspaceAuth/updateWorkspaceAuth',
            payload: {}
          });
        }}
        className={styles['workspace-select']}
        style={{width: 130}}
        dropdownMatchSelectWidth={false}
        dropdownClassName={styles['workspace-select-dropdown']}
        dropdownRender={(menu) => (
          <>
            {menu}
            <div className={styles['workspace-select-dropdown-footer']}>
              前往<Link onClick={() => navigate(urls.manageWorkspace)}>全部空间</Link>
            </div>
          </>
        )}
      />
    ),
    [workspaceList, loading, workspaceId, setUrlParams]
  );

  // 标题信息
  const titleInfo = useMemo(
    () => ({
      title: (
        <div className={styles['header-title']} onClick={() => navigate('/')}>
          <div className={styles['header-title-icon']} />
        </div>
      )
    }),
    []
  );

  // 退出登录
  const accountLogout = () => {
    // 获取framework退出登录按钮
    const exitElement = document.querySelector(
      '#bce-content .header .content #user-nav .iam-antd-global :nth-child(4) :nth-child(2)'
    );

    if (exitElement) {
      const clickEvent = new MouseEvent('click', {
        bubbles: true,
        cancelable: true,
        view: window
      });

      exitElement.dispatchEvent(clickEvent);
    }
  };

  // TODO：修改个人信息内容和链接
  const menuList = useMemo(
    () => [
      {
        key: 'personalCenter',
        label: '我的账户',
        clickHandler: () => window.open('https://console.bce.baidu.com/iam/#/iam/baseinfo', '_blank')
      },
      {
        key: 'billing',
        label: '财务中心',
        clickHandler: () => window.open('https://console.bce.baidu.com/billing/#/account/index', '_blank')
      },
      {
        key: 'security',
        label: '安全中心',
        clickHandler: () => window.open('https://console.bce.baidu.com/iam/#/iam/accesslist', '_blank')
      },
      {
        key: 'multiUser',
        label: '多用户访问控制',
        clickHandler: () => window.open('https://console.bce.baidu.com/iam/#/iam/overview', '_blank')
      }
    ],
    []
  );

  // 右侧信息栏
  const rightInfo = useMemo(() => {
    return (
      <>
        <div className={styles['right-info']}>
          <div className={styles['right-info-dropdown']}>
            {infoList.map((item) => (
              <div key={item.name} className={styles['right-info-dropdown-item']}>
                <Dropdown overlay={RightMenu({items: item.children})}>
                  <IconSvg type={item.icon} size={28}></IconSvg>
                </Dropdown>
              </div>
            ))}
          </div>
          <AvatarDropdown
            userName={decodeURIComponent(userName || '') || '百度智能云用户'}
            header
            logout={accountLogout}
            menuList={menuList}
          />
        </div>
      </>
    );
  }, [currentRegion.label, menuList, userName]);

  return (
    <div className={cx(styles['workspace-header'], styles['db-workspace-header'])}>
      <Menu
        mode="horizontal"
        scope="global"
        className={styles['menu']}
        headerMenu={WorkspaceSelect}
        otherArea={rightInfo}
        titleInfo={titleInfo}
        logoInfo={{logo: undefined}}
      />
    </div>
  );
};

export default Header;
