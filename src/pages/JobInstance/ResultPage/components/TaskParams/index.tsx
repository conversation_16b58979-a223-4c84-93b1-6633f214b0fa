import DescriptionList from '@pages/JobInstance/ResultPage/components/DescriptionList';
import {Empty, Space, Timeline} from 'acud';
import React from 'react';
import styles from './index.module.less';
import {IJobInstanceTaskNode} from '@api/jobInstance';
import IconSvg from '@components/IconSvg';
import {dealOperatorParams, dealTaskParams} from './dealParams';

// 任务参数组件
const JobInstanceTaskParams: React.FC<{
  jobInstanceTask?: IJobInstanceTaskNode;
}> = ({jobInstanceTask}) => {
  return (
    <div className={styles['task-params']}>
      <div className={styles['task-params-title']}>
        <IconSvg type="job-instance-run-params" size={16} />
        <span>运行参数</span>
      </div>
      {jobInstanceTask ? (
        <>
          <div className={styles['task-params-description-list']}>
            <DescriptionList infoList={dealTaskParams(jobInstanceTask)} />
          </div>
          {jobInstanceTask.operatorList?.length > 0 && (
            <div className={styles['task-params-title']}>
              <Space>
                <IconSvg type="job-instance-operator" size={16} />
                算子参数
              </Space>
            </div>
          )}
          <Timeline mode="vertical" className={styles['task-params-timeline']}>
            {jobInstanceTask.operatorList?.map((item) => {
              return (
                <Timeline.Item
                  label={<span style={{paddingLeft: '6px'}}>{item.name}</span>}
                  key={item.id}
                  color="#2468F2"
                >
                  <div style={{paddingLeft: '6px', marginTop: '-4px'}}>
                    <DescriptionList
                      className="task-params-timeline-description-list"
                      infoList={dealOperatorParams(item)}
                    />
                  </div>
                </Timeline.Item>
              );
            })}
          </Timeline>
        </>
      ) : (
        <Empty />
      )}
    </div>
  );
};

export default JobInstanceTaskParams;
