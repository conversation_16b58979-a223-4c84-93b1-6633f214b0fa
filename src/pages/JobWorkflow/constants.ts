/** 页面类型 */
export enum WorkflowPageType {
  JOBS = 'jobs',
  TEMPLATES = 'templates'
}

/** 任务类型 */
export enum JobTaskType {
  // ray 任务 算子为空
  RAY_TASK = 'RAY_TASK',
  // 数据流任务
  DATAFLOW_TASK = 'DATAFLOW_TASK'
}

/** 页面类型 */
export enum JobCreateType {
  // 新建空任务
  EMPTY = 'empty',
  // 新建 JSON 任务
  JSON = 'json'
}

// 页面类型 详情/实例
export enum JobDetailPageType {
  DETAIL = 'detail',
  JOB_INSTANCES = 'jobInstances'
}

/**
 * 节点类型
 * 1. 边
 * 2. 任务节点
 * 3. 算子节点
 */

export enum X6ShapeType {
  EDGE = 'edge',
  TASK = 'task-node',
  OPERATOR = 'operator-node'
}

/** 创建 类型 */
export enum WorkflowPageCreateType {
  COPY = 'copy',
  TEMPLATE = 'template'
}

// 节点大小 位置
export const NODE_SIZE = {
  [X6ShapeType.TASK]: {
    width: 300,
    height: 52,
    titleHeight: 52,
    gutter: 60
  },
  [X6ShapeType.OPERATOR]: {
    width: 276,
    height: 32,
    gutter: 24,
    paddingLeft: 12
  }
};
