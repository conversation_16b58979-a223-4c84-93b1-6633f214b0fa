import React, {useEffect, useRef, useState} from 'react';
import {Input, InputNumber, Space} from 'acud';
import {Graph, Node} from '@antv/x6';
import {MiniMap} from '@antv/x6-plugin-minimap';
import {register} from '@antv/x6-react-shape';
import {Scroller} from '@antv/x6-plugin-scroller';

import {dealJsonToX6} from './dealX6';
import styles from './index.module.less';
import {JobTaskType, NODE_SIZE, X6ShapeType} from '@pages/JobWorkflow/constants';
import {
  OperatorNodeComponent,
  SimpleNodeOperatorView,
  SimpleNodeTaskView,
  TaskNodeComponent,
  ZoomComponent
} from './x6-component';

// 注册任务节点
register({
  shape: X6ShapeType.TASK,
  width: 160,
  height: 120,
  // 监听数据变化
  effect: ['data'],
  component: TaskNodeComponent
});

// 注册算子节点
register({
  shape: X6ShapeType.OPERATOR,
  width: 80,
  height: 40,
  // 监听数据变化
  effect: ['data'],
  component: OperatorNodeComponent
});
//定义类型
interface IX6Props {
  json?: string;
}
// json 转为 x6 图形
const X6Page: React.FC<IX6Props> = ({json}) => {
  const graphRef = useRef<Graph>();
  const containerRef = useRef<HTMLDivElement>(null);
  const minimapRef = useRef<HTMLDivElement>(null);
  const [scale, setScale] = useState(100);
  // 加载 json 数据到 x6 dealJsonToX6
  const loadJson = () => {
    if (graphRef.current) {
      try {
        const obj: any = JSON.parse(json || '{}');
        graphRef.current.fromJSON(dealJsonToX6(obj));
      } catch (error) {
        console.log(error);
      }
    }
  };
  // 初始化 x6 图形
  const initGraph = () => {
    const graph = new Graph({
      container: containerRef.current!,
      background: {
        color: '#FBFBFC'
      },

      grid: true, // 网格
      interacting: {
        nodeMovable: false // 禁止节点移动
      },
      scaling: {min: 0.01, max: 5},
      mousewheel: {enabled: true, factor: 1.05},
      autoResize: true, // 自适应大小
      panning: true, // 平移 与滚动画布有冲突
      embedding: {
        enabled: true, // 自动吸附
        findParent({node}) {
          const bbox = node.getBBox();
          return this.getNodes().filter((node) => {
            const data = node.getData<{parent: boolean}>();
            if (data && data.parent) {
              const targetBBox = node.getBBox();
              return bbox.isIntersectWithRect(targetBBox);
            }
            return false;
          });
        }
      }
    });
    graph.on('resize', ({width}) => {
      graph.zoomTo(1);
      // const {width} = graph.getGraphArea();
      graph.translate((width - NODE_SIZE[X6ShapeType.TASK].width) / 2, 10);
    });
    graph.on('scale', ({sx}) => {
      setScale(sx * 100);
    });
    // 缩略图
    graph.use(
      new MiniMap({
        container: minimapRef.current!,
        graphOptions: {
          createCellView(cell) {
            if (cell.isEdge()) {
              return null;
            }
            if (cell.isNode() && cell.shape === X6ShapeType.OPERATOR) {
              return SimpleNodeOperatorView;
            }
            if (cell.isNode() && cell.shape === X6ShapeType.TASK) {
              return SimpleNodeTaskView;
            }
          }
        },
        width: 132,
        height: 84,
        padding: 10,
        maxScale: 2
      })
    );

    graphRef.current = graph;
  };

  // 初始化 x6 图形
  useEffect(() => {
    initGraph();
    return () => {
      // 延迟销毁，避免销毁时，调整页面大小，导致页面处理
      setTimeout(() => {
        console.log('销毁 X6 Graph');
        graphRef.current?.dispose(); // 清理 Graph 实例
        graphRef.current = null;
      }, 10);
    };
  }, []);

  // 监听 json 数据变化，重新加载 x6 图形
  useEffect(() => {
    loadJson();
  }, [json]);

  // 缩放
  const onChangeZoom = (num: number) => {
    graphRef.current?.zoomTo(num / 100);
  };

  return (
    <div className={styles['x6-main']}>
      <div className={styles['zoom-btn']}>
        <ZoomComponent value={scale} onChange={onChangeZoom} />
      </div>
      <div ref={containerRef} className={styles['x6-content']} />

      <div className={styles['minimap-container']} ref={minimapRef} />
    </div>
  );
};

export default React.memo(X6Page);
