/**
 * 面包屑导航组件
 * <AUTHOR>
 */
import React, {useEffect, useState} from 'react';
import {type GetWorkspaceFolderListResult} from '@api/WorkArea';
import classNames from 'classnames/bind';
import styles from './index.module.less';
import IconSvg from '@components/IconSvg';
import {Ellipsis} from '@baidu/bce-react-toolkit';
import {Dropdown, Menu} from 'acud';

const cx = classNames.bind(styles);

interface BreadcrumbProps {
  path?: GetWorkspaceFolderListResult[];
  onItemClick?: (item: GetWorkspaceFolderListResult) => void;
}

// 面包屑项目类型 继承 GetWorkspaceFolderListResult 并添加 children 属性展示更多
interface WorkspaceFolderBreadcrumb extends GetWorkspaceFolderListResult {
  children?: GetWorkspaceFolderListResult[];
}

const Breadcrumb: React.FC<BreadcrumbProps> = ({path, onItemClick}) => {
  // 面包屑最大展示数量 需要大于等于 3
  const maxLength = 5;
  // 面包屑列表
  const [breadcrumbList, setBreadcrumbList] = useState<WorkspaceFolderBreadcrumb[]>([]);

  useEffect(() => {
    const innerPath = path?.slice(0, -1) || [];
    // 若数量过多，则只展示第一个和后几个，中间用...展示
    if (innerPath.length > maxLength) {
      setBreadcrumbList([
        innerPath[0],
        {
          id: 'more-id',
          name: '',
          children: innerPath.slice(1, -(maxLength - 2)),
          path: '',
          parentId: '',
          creator: '',
          createdAt: '',
          type: ''
        },
        ...innerPath.slice(-(maxLength - 2))
      ]);
    } else {
      setBreadcrumbList(innerPath);
    }
  }, [path]);

  // 面包屑项目点击处理
  const handleItemClick = (item: GetWorkspaceFolderListResult) => {
    if (onItemClick) {
      onItemClick(item);
    }
  };

  return (
    <div className={cx('flex items-center')}>
      {breadcrumbList?.map((item, index) => {
        const lastOne = index === breadcrumbList.length - 1;
        // 若存在children，则展示下拉菜单
        if (item.children) {
          return (
            <span className={cx('breadcrumb-item')} key={item.id}>
              <Dropdown
                label={
                  <span className={cx('item-text')}>
                    ...
                    <span className={cx('bread-icon')}>
                      <IconSvg size={14} type="right" />
                    </span>
                  </span>
                }
                overlay={
                  <Menu>
                    {item.children.map((child) => (
                      <Menu.Item key={child.id} onClick={() => handleItemClick(child)}>
                        <Ellipsis width={200} tooltip={child.name}>
                          {child.name}
                        </Ellipsis>
                      </Menu.Item>
                    ))}
                  </Menu>
                }
              />
            </span>
          );
        }

        return (
          <span className={cx('breadcrumb-item', {'last-item': lastOne})} key={item.id}>
            <span
              key={item.id}
              className={cx('item-text', {
                'before-item-text': !lastOne
              })}
              onClick={() => {
                handleItemClick(item);
              }}
            >
              <Ellipsis tooltip={item.name}>{item.name}</Ellipsis>
            </span>
            {!lastOne && (
              <span className={cx('bread-icon')}>
                <IconSvg size={14} type="right" />
              </span>
            )}
          </span>
        );
      })}
    </div>
  );
};

export default Breadcrumb;
