/**
 * 新建弹窗：Catalog、Schema
 * * 注意：
 * * * 1、不包括 Volume、Table、Operator的弹窗，该弹窗为简单的名称 + 描述
 * <AUTHOR>
 */

import React, {useState} from 'react';
import {Form, Input, Modal, toast} from 'acud';
import {RuleObject} from 'acud/lib/form';
import {createCatalog, createSchema, createTable, createOperator} from '@api/metaRequest';
import {formItemLayout} from '../helper';
import styles from './ModalStyle.module.less';

type IrequestFun = typeof createCatalog | typeof createSchema | typeof createTable | typeof createOperator;

interface IModalCreateNameProps {
  visible: boolean; // 弹窗可见
  setVisible: React.Dispatch<React.SetStateAction<boolean>>; // 更改弹窗可见
  title: string; // 弹窗标题 & 表单名称的 label
  nameRules?: RuleObject[]; // 表单名称校验规则
  requestFun: IrequestFun; // 点击确定创建的请求方法
  requestParams?: {[key: string]: any}; // 额外的请求参数，例如：Catalog 创建需要 workspaceId
  successFun: (...args: any[]) => any; // requestFun 请求成功后回调
  limitLength?: number; // 输入框最大长度
  commentLimitLength?: number; // 描述输入框最大长度
  forbidIfLimit?: boolean; // 超出长度是否禁用
}

const ModalCreateName = (props: IModalCreateNameProps) => {
  const {
    visible,
    setVisible,
    title,
    requestFun,
    requestParams,
    nameRules = [],
    successFun,
    limitLength = 64,
    commentLimitLength = 150,
    forbidIfLimit = true
  } = props;

  const [form] = Form.useForm();

  // 确定按钮 loading
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);

  const handleCancel = () => {
    setVisible(false);
    setConfirmLoading(false);
  };

  const handleOk = async () => {
    try {
      await form.validateFields();
    } catch {
      return;
    }
    // formData 值为： {name:xxx, comment:xxx}
    const formData = form.getFieldsValue();
    setConfirmLoading(true);
    try {
      const res = await requestFun({...requestParams, ...formData});
      if (res?.success != true) {
        setConfirmLoading(false);
        return;
      }
      toast.success({
        message: '创建成功！',
        duration: 5
      });
      // 请求成功后的回调
      successFun(formData);
      handleCancel();
    } catch {
      setConfirmLoading(false);
      return;
    }
  };

  return (
    <Modal
      className={styles['modal-form-reset-acud']}
      title={`创建 ${title}`}
      visible={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      afterClose={() => form.resetFields()}
      confirmLoading={confirmLoading}
    >
      <Form {...formItemLayout} form={form} name="create-name-modal">
        <Form.Item
          name="name"
          label={`${title}名称`}
          rules={[
            {
              required: true,
              message: '名称不可为空'
            },
            ...nameRules
          ]}
        >
          <Input placeholder="请输入名称" limitLength={limitLength} forbidIfLimit={forbidIfLimit} />
        </Form.Item>
        <Form.Item name="comment" label="描述">
          <Input.TextArea
            placeholder="请输入描述"
            allowClear={false}
            limitLength={commentLimitLength}
            forbidIfLimit={forbidIfLimit}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ModalCreateName;
