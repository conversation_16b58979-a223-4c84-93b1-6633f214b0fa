/**
 * 子网选择组件
 * 该组件主要提供了一个根据vpcId来选择可用区和子网的功能
 * <AUTHOR>
 */
import React, {useEffect, useState} from 'react';
import {Select} from 'acud';
import {getSubnetList} from '@api/Compute';
import _ from 'lodash';
import RefreshButton from '@components/RefreshButton';
import {useRequest} from 'ahooks';
import styles from './index.module.less';
import classNames from 'classnames/bind';
import {ZoneOption} from '../index';

const cx = classNames.bind(styles);

interface SubnetSelectProps {
  vpcId?: string;
  zone?: string;
  value?: string;
  onChange?: (value?: string) => void;
}

const SubnetSelect: React.FC<SubnetSelectProps> = ({vpcId = '', zone, value, onChange}) => {
  const [subnetId, setSubnetId] = useState(value);

  const [subnetList, setSubnetList] = useState<Array<{label: string; value: string; zone: string}>>([]);

  // 计算子网列表，添加禁用状态
  const subnetListExtra = React.useMemo(() => {
    return _.map(subnetList, (item) => ({
      ...item,
      disabled: !!(zone && item.zone !== zone)
    }));
  }, [subnetList, zone]);

  useEffect(() => {
    setSubnetId(value);
  }, [value]);

  const {loading, run: getSubnetOptions} = useRequest(
    async () => {
      const {page} = await getSubnetList({vpcId});
      const list =
        page?.result?.map((item) => ({
          label: item.name,
          value: item.subnetId,
          zone: item.az
        })) || [];
      setSubnetList(list);
    },
    {
      manual: true
    }
  );

  useEffect(() => {
    if (vpcId) {
      getSubnetOptions();
    }
  }, [vpcId, getSubnetOptions]);

  useEffect(() => {
    const subnet = subnetListExtra.find((item) => item.zone === zone);
    setSubnetId(subnet?.value);
    onChange?.(subnet?.value);
  }, [zone, subnetListExtra]);

  return (
    <div className={cx('subnet-select', 'flex')}>
      <Select
        loading={loading}
        value={subnetId}
        options={subnetListExtra}
        placeholder="请选择子网"
        className="flex-1"
        onChange={(value) => {
          setSubnetId(value);
          onChange?.(value);
        }}
      />
      <RefreshButton className="ml-[8px]" onClick={getSubnetOptions} />
    </div>
  );
};

export default SubnetSelect;
