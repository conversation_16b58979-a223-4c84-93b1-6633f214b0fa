.tag-status {
  background: #ffffff;
  border: 1px solid #d4d6d9;
  border-radius: 16px;
  height: 28px;
  display: flex;
  gap: 4px;
  font-size: 12px;
  line-height: 26px;
  padding: 0 9px;
  align-items: center; /* 让所有子元素垂直居中 */

  &.checked {
    background: #e6f0ff;
    border: 1px solid #2468f2;
    font-weight: bold;
  }
  &:hover {
    background: rgba(7, 12, 20, 0.06);
    cursor: pointer;
  }

  .circle {
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 50%;
  }

  .name {
    display: inline-block;
  }

  .number {
    display: inline-block;
  }
}
