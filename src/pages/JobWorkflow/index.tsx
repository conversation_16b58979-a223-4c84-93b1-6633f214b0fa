/**
 * JobWorkflow 工作流入口口文件
 * 主要功能:
 *  1.  Jobs: 工作流
 *  2.  Templates: 工作流模板
 * 基本逻辑:
 * 1. 使用 Tabs 组件切换工作流列表和工作流模板列表
 *
 * <AUTHOR>
 */
import React from 'react';
import useUrlState from '@ahooksjs/use-url-state';
import {Tabs} from 'acud';
import Jobs from './Jobs';
import Templates from './Templates';
import {WorkflowPageType} from './constants';
const {TabPane} = Tabs;

const JobWorkflow: React.FC = () => {
  // 区分 列表与模板页面
  const [state, setState] = useUrlState<{tabType: string}>({
    tabType: WorkflowPageType.JOBS
  });

  // 切换 修改 url 参数 保证刷新也可以正常切换页面
  const onChange = (activeKey: string) => {
    setState({tabType: activeKey});
  };
  return (
    <div className={`db-workspace-wrapper `} style={{paddingTop: '10px'}}>
      <Tabs onChange={onChange} activeKey={state.tabType}>
        <TabPane
          tab={<span style={{fontSize: '18px', lineHeight: '20px', fontWeight: 500}}>工作流列表</span>}
          key={WorkflowPageType.JOBS}
        >
          <Jobs />
        </TabPane>
        <TabPane
          tab={<span style={{fontSize: '18px', lineHeight: '20px', fontWeight: 500}}>预置模板</span>}
          key={WorkflowPageType.TEMPLATES}
        >
          <Templates />
        </TabPane>
      </Tabs>
    </div>
  );
};

export default JobWorkflow;
