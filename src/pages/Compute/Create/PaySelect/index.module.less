.pay-select {
  display: flex;
  gap: 16px;
  .pay-option {
    width: 230px;
    height: 62px;
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    gap: 16px;
    .pay-option-icon {
      width: 24px;
      height: 24px;
      flex: none;
    }
    .pay-option-label-text {
      font-size: 14px;
      color: #151B26;
      line-height: 22px;
      font-weight: bold;
    }
    .pay-option-label-desc {
      font-size: 12px;
      color: #5C5F66;
      line-height: 20px;
    }
    &.selected {
      border: 1px solid #2468F2;
      background: #EEF3FE;
    }
    &.disabled {
      background: #F7F7F9;
      border: 1px solid #D4D6D9;
      cursor: not-allowed;
      position: relative;

      &::after {
        content: '敬请期待';
        position: absolute;
        top: 0;
        right: 0;
        padding-right: 6px;
        padding-left: 10px;
        height: 20px;
        line-height: 20px;
        background-color: rgba(132, 134, 140, 0.7);
        color: #fff;
        font-size: 12px;
        border-top-right-radius: 6px;
        clip-path: polygon(0 0, 100% 0, 100% 100%, 10px 100%);
      }
    }

    &.postpaid {
      .pay-option-icon {
        background-image: url(../../../../assets/originSvg/compute-post.svg?url);
      }
    }

    &.prepaid {
      .pay-option-icon {
        background-image: url(../../../../assets/originSvg/compute-pre.svg?url);
      }
    }
  }
}