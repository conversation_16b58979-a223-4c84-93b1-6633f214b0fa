import moment from 'moment';

/**
 * 格式化时间字符串为指定格式的日期时间字符串
 *
 * @param {string} time - 要格式化的时间字符串
 * @param {string} [format='YYYY-MM-DD HH:mm:ss'] - 目标时间格式，默认为 'YYYY-MM-DD HH:mm:ss'
 * @returns {string} 格式化后的时间字符串，如果时间无效，则返回 '-'
 */
export const formatTime = (
  time: string,
  format: string = 'YYYY-MM-DD HH:mm:ss'
) => {
  if (!time || time === '0001-01-01T00:00:00Z') {
    return '-';
  }
  return moment(time).format(format);
};
