const {defineConfig} = require('@baidu/cba-cli');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const path = require('path');
const MonacoWebpackPlugin = require('monaco-editor-webpack-plugin');

const templateId = '3a443985-cea0-4b7c-892e-6a826c46f4c5'; // 复用功能清单 【EDAP-私有化最新版】 项目

// 自定义本地服务 host  & prot
const devHost = 'localhost.bigdata.agilecloud-yq.com';
const devPort = 8899;
// 自定义代理地址
const proxyTarget = 'http://bigdata.agilecloud-yq.com';

module.exports = defineConfig({
  appName: 'databuilder',
  htmlTemplate: './private-common/private-index.html', // 路径是相对 process.cwd() 的
  presets: ['@baidu/cba-preset-react'],
  babelOptions: {
    plugins: ['@babel/plugin-proposal-optional-chaining'],
    resolveOptions: (babelConfig) => {
      babelConfig.presets = []; // 让其使用本地 .babelrc 配置
      return babelConfig;
    }
  },

  port: devPort,
  host: devHost,
  https: false,
  caching: true,
  rules: ['/api/databuilder/(.*)'],
  root: '.mockrc',
  proxyTarget, // 代理到 DB 私有化测试环境
  publicPath: '/databuilder/', // 设置 webpack dev server 的根路径
  openPath: '/databuilder/', // 设置自动打开浏览器时的路径
  i18n: {
    enabled: true,
    independent: false
  },
  templateId, // 功能清单ID
  flags: ['databuilder'], // 功能清单项, 要配置 DB 产品的功能清单
  webpack(config, merge) {
    // 这里merge无法生效，需要手动合并
    config.module.rules[0].oneOf.unshift({
      test: /tailwind\.css$/,
      use: [
        MiniCssExtractPlugin.loader,
        'css-loader',
        {
          loader: 'postcss-loader',
          options: {
            postcssOptions: {
              plugins: ['tailwindcss', 'postcss-preset-env']
            }
          }
        }
      ]
    });

    // 添加 devServer 代理配置
    if (config.devServer) {
      config.devServer.proxy = {
        '/databuilder/api/databuilder/v1': {
          target: 'http://bigdata.agilecloud-yq.com',
          changeOrigin: true,
          secure: false,
          logLevel: 'debug'
        },
        '/pms/v1/user/loginStatus': {
          target: 'http://bigdata.agilecloud-yq.com',
          changeOrigin: true,
          secure: false,
          logLevel: 'debug'
        }
      };

      // 设置自动打开浏览器时的路径
      if (typeof config.devServer.open === 'boolean' && config.devServer.open) {
        config.devServer.open = {target: '/databuilder/'};
      } else if (typeof config.devServer.open === 'object') {
        config.devServer.open.target = '/databuilder/';
      }
    }

    return merge(config, {
      module: {
        rules: [
          {
            // 此规则用于解决 webpack5 中的模块解析问题，针对 @baidu/xicon-react-bigdata 的解析问题
            test: /\.m?js/,
            resolve: {
              fullySpecified: false
            }
          }
        ]
      },
      resolve: {
        alias: {
          '@pages': path.resolve('./src/pages'),
          '@components': path.resolve('./src/components'),
          '@hooks': path.resolve('./src/hooks'),
          '@utils': path.resolve('./src/utils'),
          '@api': path.resolve('./src/api'),
          '@assets': path.resolve('./src/assets'),
          '@styles': path.resolve('./src/styles'),
          '@type': path.resolve('./src/type'),
          '@store': path.resolve('./src/store'),
          '@helpers': path.resolve('./src/helpers')
        }
      },
      plugins: [new MonacoWebpackPlugin()]
    });
  }
});
