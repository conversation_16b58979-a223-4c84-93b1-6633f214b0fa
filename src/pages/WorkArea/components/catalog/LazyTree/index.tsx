/**
 * 树形目录组件
 * 该组件用来展示目录结构
 * 通过getWorkspaceFolderList来获取目录结构
 * 通过onSelect来设置当前的目录
 * <AUTHOR>
 */
import React, {useState, useEffect, useContext} from 'react';
import _ from 'lodash';
import {Tree} from 'acud';
import type {DataNode} from 'acud/es/tree';
import type {GetWorkspaceFolderListResult} from '@api/WorkArea';
import classNames from 'classnames/bind';
import styles from './index.module.less';
import {WorkspaceContext} from '@pages/index';
import {loadTreedData} from '../index';
import IconSvg from '@components/IconSvg';
import {FOLDER_NAME_MAP} from '@pages/WorkArea/config';

const cx = classNames.bind(styles);

interface CatalogTreeProps {
  onSelect?: (businessData?: GetWorkspaceFolderListResult) => void;
  currentKey?: React.Key;
  dataSource: DataNode[];
  onTreeDataChange?: (key: React.Key, children: DataNode[]) => void;
}

interface treeNode extends DataNode {
  businessData?: GetWorkspaceFolderListResult;
}

const CatalogTree: React.FC<CatalogTreeProps> = ({currentKey, dataSource, onTreeDataChange, onSelect}) => {
  const initKey = currentKey ? [currentKey] : [];
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>(initKey);
  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>(initKey);
  const [loadedKeys, setLoadedKeys] = useState<React.Key[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const {workspaceId} = useContext(WorkspaceContext);

  useEffect(() => {
    if (currentKey) {
      setSelectedKeys([currentKey]);
    }
  }, [currentKey]);

  const onLoadData = async (node: treeNode) => {
    if (loadedKeys.includes(node.key) || node.isLeaf) {
      return;
    }

    setLoading(true);
    try {
      const children = await loadTreedData({
        workspaceId,
        nodeId: node.key,
        titleMap: _.pick(FOLDER_NAME_MAP, ['TRASH', 'USERS'])
      });
      onTreeDataChange?.(node.key, children);
      setLoadedKeys([...loadedKeys, node.key]);
    } catch (error) {
      console.error('加载数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const onExpand = (newExpandedKeys: React.Key[]) => {
    setExpandedKeys(newExpandedKeys);
  };

  const handleSelect = (newSelectedKeys: React.Key[], info) => {
    setSelectedKeys(newSelectedKeys);
    if (newSelectedKeys.length > 0 && info.node) {
      // 获取选中节点的业务数据
      const businessData = info.node.businessData;
      onSelect?.(businessData);
    }
  };

  const renderIcon = (node) => {
    const iconMap = {
      ALL: {type: 'workarea-all', size: 16, color: '#84868C'},
      HOME: {type: 'workarea-home', size: 16, color: '#84868C'},
      TRASH: {type: 'workarea-trash', size: 16, color: '#84868C'},
      USERS: {type: 'workarea-users', size: 16, color: '#84868C'},
      USER: {type: 'workarea-user', size: 16, color: '#84868C'},
      NORMAL: {type: 'workarea-folder', size: 16, color: '#FF9F0B'}
    };
    const iconInfo = iconMap[node.businessData.type];
    return <IconSvg {...iconInfo} />;
  };

  return (
    <Tree
      titleEllipsis
      icon={renderIcon}
      className={cx('w-full')}
      loadData={onLoadData}
      treeData={dataSource}
      expandedKeys={expandedKeys}
      selectedKeys={selectedKeys}
      onExpand={onExpand}
      onSelect={handleSelect}
    />
  );
};

export default CatalogTree;
