import Enum from '@helpers/enum';
export const NAME_LIMIT_LENGTH = 64;
export const NAME_REGEX = /^([a-zA-Z\u4e00-\u9fa5][a-zA-Z0-9\u4e00-\u9fa5\-_/.]{0,63})?$/;
export const NAME_ERROR_MESSAGE =
  '由大小写字母、数字、中文以及-_/.特殊字符，必须以字母或者中文开头，长度1-64';

export const PAY_TYPE = new Enum(
  {
    alias: 'PREPAID',
    text: '包年包月',
    value: 'Prepaid',
    desc: '先付费后使用，价格更低廉',
    className: 'prepaid'
  },
  {
    alias: 'POSTPAID',
    text: '按量付费',
    value: 'Postpaid',
    desc: '先使用后付费，按需开通',
    className: 'postpaid'
  }
);

export const STATUS = new Enum(
  {
    alias: 'DEPLOY',
    text: '生效中',
    value: 'DEPLOY',
    className: 'deploying'
  },
  {
    alias: 'RUNNING',
    text: '运行中',
    value: 'RUNNING',
    className: 'running'
  },
  {
    alias: 'INVALID',
    text: '失效',
    value: 'INVALID',
    className: 'invalid'
  },
  {
    alias: 'CREATED_FAIL',
    text: '创建失败',
    value: 'CREATED_FAIL',
    className: 'created-fail'
  }
);
