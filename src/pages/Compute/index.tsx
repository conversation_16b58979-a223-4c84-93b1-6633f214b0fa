/**
 * 计算资源列表组件
 * 该页面主要用于展示计算资源列表，支持搜索、分页、删除操作
 * 主要逻辑：
 * 1. 获取计算资源列表
 * 2. 渲染列表
 * 3. 搜索功能
 *    - 通过Search组件实现
 * 4. 分页功能
 *    - 通过Pagination组件实现
 * 5. 删除功能
 *    - 通过Modal组件实现
 *    - 通过deleteComputeResource api实现
 * <AUTHOR>
 */
import React, {useState, useCallback, useEffect, useContext} from 'react';
import {Table, Button, Pagination, toast, Modal, Loading} from 'acud';
import {Plus1} from '@baidu/xicon-react-bigdata';
import {getComputeResourceList, ComputeResourceItem, deleteComputeResource} from '@api/Compute';
import {useNavigate} from 'react-router-dom';
import urls from '@utils/urls';
import {WorkspaceContext} from '@pages/index';
import RefreshButton from '@components/RefreshButton';
import styles from './index.module.less';
import classNames from 'classnames/bind';
import {PAY_TYPE, STATUS} from './config';
import useAuth from '@hooks/useAuth';

const cx = classNames.bind(styles);

const ComputeResourceList: React.FC = () => {
  const [dataSource, setDataSource] = useState<ComputeResourceItem[]>([]);
  const [pageNo, setPageNo] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const {workspaceId} = useContext(WorkspaceContext);
  const [loading, setLoading] = useState(true);
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc' | undefined>(undefined);
  const [sortField, setSortField] = useState<string | undefined>(undefined);
  const initStatus = [];
  const [statusFilter, setStatusFilter] = useState<string[]>(initStatus as string[]);

  const navigate = useNavigate();
  const computeReadOnly = useAuth('workspace', 'compute') !== 'readWrite';

  const loadComputeList = useCallback(async () => {
    setLoading(true);
    const res = await getComputeResourceList({
      pageNo,
      pageSize,
      workspaceId,
      order: sortOrder,
      orderBy: sortField,
      status: statusFilter?.join(',') || ''
    });
    if (res.success) {
      setTotal(res.result.total);
      setDataSource(res.result.computes);
    }
    setLoading(false);
  }, [pageNo, pageSize, sortOrder, sortField, workspaceId, statusFilter]);

  useEffect(() => {
    loadComputeList();
  }, [loadComputeList]);

  const handlePageChange = useCallback((page: number, size: number) => {
    setPageNo(page);
    setPageSize(size);
  }, []);

  const columns = [
    {
      title: '集群ID',
      dataIndex: 'computeId',
      key: 'computeId',
      width: 150
    },
    {
      title: '集群名称',
      dataIndex: 'name',
      key: 'name',
      width: 200
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      filterMultiple: true,
      filters: STATUS.toArray(),
      filteredValue: statusFilter,
      render: (status: string) => {
        const statusObj = STATUS.fromValue(status);
        return <span className={cx('compute-status', statusObj.className)}>{statusObj.text}</span>;
      }
    },
    {
      title: '引擎类型',
      dataIndex: 'engine',
      key: 'engine',
      width: 120
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      sorter: true,
      width: 180
    },
    {
      title: '付费方式',
      dataIndex: 'chargeType',
      key: 'chargeType',
      width: 120,
      render: (value: string) => {
        return PAY_TYPE.getTextFromValue(value);
      }
    },
    !computeReadOnly
      ? {
          title: '操作',
          key: 'operation',
          width: 150,
          render: (record: ComputeResourceItem) => {
            return (
              <a
                onClick={() => {
                  Modal.confirm({
                    title: '删除集群',
                    content:
                      '集群删除后不可恢复，关联任务的运行状态会受影响，关联任务需要重新指定计算集群。确定删除集群？',
                    onOk: async () => {
                      const res = await deleteComputeResource({
                        workspaceId,
                        computeId: record.computeId || ''
                      });
                      if (res.success) {
                        toast.success({
                          message: '删除成功',
                          duration: 3
                        });
                        loadComputeList();
                      }
                    }
                  });
                }}
              >
                删除
              </a>
            );
          }
        }
      : undefined
  ].filter(Boolean);

  const handleTableChange = useCallback((pagination, filters, sorter) => {
    setSortField(sorter.field);

    const orderMap = {
      ascend: 'asc',
      descend: 'desc'
    };
    setSortOrder(orderMap[sorter.order]);

    setStatusFilter(filters.status as string[]);
  }, []);

  const showBlankSpace = !computeReadOnly && dataSource.length === 0 && !statusFilter?.length;
  return (
    <div className={cx('db-workspace-wrapper', 'compute-wrapper')}>
      <div className={cx('compute-title', 'mb-[16px]')}>常驻集群</div>
      {loading ? (
        <Loading loading />
      ) : (
        <>
          {showBlankSpace ? (
            <div className={cx('blank-space', 'h-full')}>
              <div className={cx('blank-title')}>创建常驻集群</div>
              <div className={cx('blank-desc')}>创建常驻计算集群用于运行工作流任务</div>
              <Button
                className={cx('blank-btn')}
                type="primary"
                icon={<Plus1 className="w-4 h-4" />}
                onClick={() => navigate(`${urls.computeCreate}?workspaceId=${workspaceId}`)}
              >
                立即创建
              </Button>
              <div className={cx('blank-img')}></div>
            </div>
          ) : (
            <>
              <div className="flex justify-end mb-4">
                <RefreshButton onClick={loadComputeList} />
                {!computeReadOnly && (
                  <Button
                    className="ml-[8px]"
                    type="primary"
                    icon={<Plus1 className="w-4 h-4" />}
                    onClick={() => navigate(`${urls.computeCreate}?workspaceId=${workspaceId}`)}
                  >
                    创建集群
                  </Button>
                )}
              </div>

              <Table
                className={cx('compute-table')}
                columns={columns}
                dataSource={dataSource}
                loading={loading}
                rowKey="computeId"
                pagination={false}
                onChange={handleTableChange}
              />

              <div className={cx('pagination-wrapper', 'flex justify-end mt-4')}>
                <Pagination
                  current={pageNo}
                  pageSize={pageSize}
                  total={total}
                  showTotal={(total) => `共 ${total} 条`}
                  onChange={(page, pageSize) => handlePageChange(page, pageSize || 10)}
                  showSizeChanger
                />
              </div>
            </>
          )}
        </>
      )}
    </div>
  );
};

export default ComputeResourceList;
