/**
 *  acud全局样式覆盖
 */

// table
.acud-table {
  .acud-table-thead > tr > th {
    font-family: PingFangSC-Medium;
    color: #151b26;
  }

  .acud-table-tbody > tr.acud-table-row:hover > td,
  .acud-table-tbody > tr > td.acud-table-cell-row-hover,
  .acud-table-tbody > tr:hover {
    background: #f7f7f9;
  }

  .acud-table-tbody > tr > td,
  .acud-table-thead > tr > th,
  .acud-table tfoot > tr > td,
  .acud-table tfoot > tr > th {
    padding: 8px 12px;
  }

  .acud-table-tbody > tr > td.acud-table-selection-column,
  .acud-table-thead > tr > th.acud-table-selection-column,
  .acud-table tfoot > tr > td.acud-table-selection-column,
  .acud-table tfoot > tr > th.acud-table-selection-column {
    padding-right: 0;
  }
}

.acud-table.acud-table-empty {
  .acud-table-tbody > tr:hover {
    background: transparent;
  }
}

// tooltip
.acud-tooltip {
  .acud-tooltip-content {
    .acud-tooltip-arrow {
      .acud-tooltip-arrow-content {
        background-color: rgba(7, 12, 20, 0.85);
      }
    }

    .acud-tooltip-inner {
      color: #fff;
      background-color: rgba(7, 12, 20, 0.85);

      button:not(:hover) {
        color: #fff;
      }
    }
  }
}

// form基础控件
.acud-input,
.acud-input-textarea,
.acud-select:not(.acud-select-customize-input) .acud-select-selector,
.acud-picker {
  border-radius: 6px;
  border-color: #d4d6d9;
}

.acud-select-arrow {
  color: #84868c;
}

.acud-dropdown {
  border-radius: 6px !important;

  .acud-dropdown-menu {
    .acud-dropdown-menu-item:hover,
    li.acud-dropdown-menu-submenu:hover {
      background: rgba(7, 12, 20, 0.06);
    }

    .acud-dropdown-menu-item,
    .acud-dropdown-menu-submenu-title {
      margin: 0 6px;
      border-radius: 6px;
      padding-left: 8px;
    }
  }
}

.acud-select-dropdown {
  border-radius: 6px !important;

  .acud-select-item.acud-select-item-option {
    margin: 0 6px;
    border-radius: 6px;
    padding-left: 8px;
  }

  .acud-select-item.acud-select-item-option.acud-select-item-option-active {
    background: rgba(7, 12, 20, 0.06);
  }
}

.acud-input-textarea-limit-box {
  right: 12px;
}

// form表单
.acud-form-vertical {
  .acud-form-item-label {
    padding-bottom: 4px;
  }
}

.acud-form {
  .acud-form-item {
    margin-bottom: 20px;
  }

  .acud-form-item-label {
    label {
      font-family: PingFangSC-Medium;
      font-size: 12px;
      color: #151b26;
      line-height: 20px;

      .acudicon.acud-form-item-tooltip {
        font-size: 16px;
        color: #84868c;
        cursor: pointer;
      }
    }

    label.acud-form-item-required {
      &::before {
        position: absolute;
        right: -12px;
      }

      .acudicon.acud-form-item-tooltip {
        position: absolute;
        right: -28px;
      }
    }
  }

  // form表单下的bos选择器样式覆盖
  .acud-form-item-has-error {
    .bos-bucket-select {
      #bos-bucket-select-path {
        .bos-bucket-select-breadcrumb {
          border-color: #f33e3e;
        }
      }
    }
  }

  .bos-bucket-select {
    #bos-bucket-select-path,
    .bos-bucket-select-breadcrumb {
      width: 100%;
      border-radius: 6px;
    }

    .bos-bucket-select-breadcrumb {
      padding-left: 12px;
    }

    #bos-bucket-select-path {
      .bos-bucket-select-popover {
        .bos-bucket-select-keyword-search {
          .acud-input {
            border-color: #e8e9eb;

            &:hover {
              border-color: #144bcc;
            }
          }

          .acud-input-span-focus {
            border-color: #144bcc;
          }
        }
      }
    }

    &:active,
    &:focus,
    &:hover {
      .bos-bucket-select-breadcrumb {
        border-color: #144bcc;
      }
    }
  }
}

// 按钮
.acud-btn {
  border-radius: 6px;
  font-family: PingFangSC-Medium;
}

.acud-btn-default {
  border-color: #d4d6d9;
}

// 带图标样式
.acud-btn.acud-btn-has-icon:not(.acud-btn-only-icon) {
  svg + span {
    margin-left: 4px;
  }
}

.acud-btn.acud-btn-lg-has-icon:not(.acud-btn-lg-only-icon) {
  svg {
    margin-right: 6px;
  }
}

// 弹窗
.acud-modal {
  .acud-modal-content {
    border-radius: 8px;

    .acud-modal-header {
      padding: 20px 20px 0;
    }

    .acud-modal-body {
      margin: 20px;
    }

    .acud-modal-footer {
      padding: 0 20px 20px;
    }
  }
}

.acud-modal-dialogbox > .acud-modal-content {
  min-height: 168px;
}

.acud-modal-dialogbox-confirm {
  .acud-modal-content {
    .acud-modal-body {
      margin-top: 12px;
    }
  }
}

// tabs
.acud-tabs-tab-active {
  font-family: PingFangSC-Medium;
}

.acud-pagination {
  .acud-pagination-item-active {
    background: #e6f0ff;
    border: 1px solid #e6f0ff;
    border-radius: 4px;
  }

  .acud-btn.acud-pagination-options-quick-jumper-button {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
}

.acud-input-search {
  .acud-input {
    &:hover {
      background: rgba(7, 12, 20, 0.06);
      border-color: #d4d6d9;

      input {
        background: transparent;
      }
    }
  }

  .acud-input-span-focus {
    border-color: #2468f2 !important;
    background: transparent !important;
  }
}

// 树样式覆盖
.acud-tree {
  .acud-tree-switcher {
    line-height: 28px;
  }

  .acud-tree-node-content-wrapper {
    min-height: 28px !important;
    line-height: 28px !important;

    .acud-tree-iconEle {
      line-height: 28px !important;
    }
  }

  .acud-tree-node-content-wrapper.acud-tree-node-selected {
    background-color: transparent !important;
    border-radius: 0 !important;
  }

  .acud-tree-node-content-wrapper:hover {
    background-color: transparent !important;
    border-radius: 0 !important;
  }

  .acud-tree-treenode-selected {
    background-color: rgba(7, 12, 20, 0.06);
    border-radius: 4px;
  }

  .acud-tree-treenode {
    line-height: 28px !important;
    padding: 0 !important;

    &:hover {
      background-color: #f7f7f9;
      border-radius: 4px;
    }
  }
}

// Checkbox 复选框
.acud-checkbox-inner {
  border-radius: 4px;
}
