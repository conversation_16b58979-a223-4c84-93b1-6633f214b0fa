import {<PERSON><PERSON><PERSON><PERSON><PERSON>, Col, Link, Loading, Row, Space, Tag, Tooltip} from 'acud';
import React, {useContext, useEffect, useRef, useState} from 'react';
import {useLocation, useNavigate} from 'react-router-dom';
import styles from './index.module.less';
import {useRequest} from 'ahooks';
import {detailJobInstance, IJobInstance, IJobInstanceTask, IJobInstanceTaskNode} from '@api/jobInstance';
import FlexDrawerArr from '@components/FlexDrawerArr';
import {IJsonNodeData} from '@pages/JobWorkflow/Jobs/components/X6Page/type';
import JobInstanceTaskParams from './components/TaskParams';
import JobInstanceTaskListTable from './components/TaskListTable';
import JobInstanceTaskLog from './components/TaskLog';
import JobInstanceTaskOperation from './components/TaskOperation';
import useUrlState from '@ahooksjs/use-url-state';
import urls from '@utils/urls';
import {WorkspaceContext} from '@pages/index';
import IconSvg from '@components/IconSvg';
import {formatSeconds} from '@utils/utils';
import {JobInstanceStatusMap} from '..';
import {JobDetailPageType} from '@pages/JobWorkflow/constants';
import {Ellipsis} from '@baidu/bce-react-toolkit';
import {JobInstanceStatus} from '../constants';
import {ExclamationCircle2} from '@baidu/xicon-react-bigdata';

// 任务结果缓存
const taskMap = new Map<string, IJobInstanceTaskNode>();

const JobInstanceResultPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const {workspaceId} = useContext(WorkspaceContext);
  const [{jobInstanceId}] = useUrlState();
  const isJobPage = location.pathname === urls.jobResult;
  // 整体的实例结果
  const [jobInstance, setJobInstance] = useState<IJobInstance>();
  // 任务列表
  const [taskList, setTaskList] = useState<IJobInstanceTaskNode[]>([]);
  // 单个任务 从缓存中提取
  const [jobInstanceTask, setJobInstanceTask] = useState<IJobInstanceTaskNode>();
  // 当前选中的任务id
  const [taskId, setTaskId] = useState('');
  // 日志刷新时间
  const [logRefreshTime, setLogRefreshTime] = useState<number>(0);

  // 处理结果列表
  const dealTaskList = (resultList: IJobInstanceTask[] = [], code: string = '{}') => {
    // code 保存的配置
    const taskArr: IJsonNodeData[] = [];
    try {
      taskArr.push(...(JSON.parse(code)?.taskDefinitionList || []));
    } catch (error) {
      console.error(error);
    }
    // 缓存 结果列表放入 map
    const resultMap = new Map<string, IJobInstanceTask>();
    resultList?.forEach((task) => {
      // TODO 删除taskId
      resultMap.set(task.id, task);
    });

    const arr: IJobInstanceTaskNode[] = [];
    // 缓存 配置 放入 map
    taskArr?.forEach((task: IJsonNodeData) => {
      const obj: IJobInstanceTaskNode = {
        ...task,
        ...resultMap.get(task.id)
      } as IJobInstanceTaskNode;
      arr.push(obj);
      taskMap.set(task.id, obj);
    });

    setTaskList([...arr]);
    if (!taskId) {
      setTaskId(arr?.[0]?.id || '');
    }
  };

  const {loading} = useRequest(() => detailJobInstance(workspaceId, jobInstanceId), {
    onSuccess: (res) => {
      const obj = res.result;
      setJobInstance(obj);
      dealTaskList(obj.taskDetailEntries, obj.code);
    }
  });

  // 切换任务，从缓存中提取
  useEffect(() => {
    if (taskId) {
      const task = taskMap.get(taskId);
      setJobInstanceTask(task);
    }
  }, [taskId]);

  // 卸载时清除缓存
  useEffect(() => {
    return () => {
      taskMap.clear();
    };
  }, []);

  return (
    <div className={styles['result-page']}>
      {loading && <Loading />}
      <div className={styles['result-container']}>
        {/* 顶部页面 */}
        <div className={styles['header']}>
          {isJobPage ? (
            <Breadcrumb>
              <Breadcrumb.Item>
                <Link onClick={() => navigate(urls.job)}>工作流列表</Link>
              </Breadcrumb.Item>
              <Breadcrumb.Item>
                <Link
                  onClick={() =>
                    navigate(
                      `${urls.jobDetail}?jobId=${jobInstance?.jobId}&type=${JobDetailPageType.JOB_INSTANCES}`
                    )
                  }
                >
                  运行记录
                </Link>
              </Breadcrumb.Item>
              <Breadcrumb.Item> </Breadcrumb.Item>
            </Breadcrumb>
          ) : (
            <Breadcrumb>
              <Breadcrumb.Item>
                <Link onClick={() => navigate(urls.jobInstance)}>运行记录</Link>
              </Breadcrumb.Item>
              <Breadcrumb.Item> </Breadcrumb.Item>
            </Breadcrumb>
          )}
          {/* 标题 */}
          <div className={styles['title']}>
            <div className={styles['title-ellipsis']}>
              <Ellipsis tooltip={jobInstance?.jobName}>{jobInstance?.jobName} </Ellipsis>
            </div>

            {/* 实例状态 */}
            <Tooltip
              title={jobInstance?.status === JobInstanceStatus.SUBMIT_FAILURE ? jobInstance?.errorMsg : ''}
            >
              <Tag
                className={styles['title-tag']}
                color={JobInstanceStatusMap[jobInstance?.jobStatus]?.color}
              >
                <div className="flex items-center gap-1">
                  {JobInstanceStatusMap[jobInstance?.jobStatus]?.label}
                  {/* 是否显示错误提示 */}
                  {jobInstance?.status === JobInstanceStatus.SUBMIT_FAILURE && (
                    <ExclamationCircle2 theme="line" size={16} strokeLinejoin="round" />
                  )}
                </div>
              </Tag>
            </Tooltip>
          </div>
          {/* 描述 */}
          <div className={styles['description']}>
            <Space split={<>|</>}>
              <span className={styles['icon-text']}>
                <IconSvg type="job-instance-id" size={14} />
                {jobInstance?.jobInstanceId}
              </span>
              <span className={styles['icon-text']}>
                <IconSvg type="job-instance-time" size={14} />
                {formatSeconds(jobInstance?.durationSec)}
              </span>
              <span className={styles['icon-text']}>
                <IconSvg type="job-instance-user" size={14} />
                {jobInstance?.runUsername}
              </span>
            </Space>
          </div>
        </div>
        {/* 内容页面 */}
        <div className={styles['content']}>
          <Row className={styles['content-row']}>
            <Col flex="1 1 0px" className={styles['content-table']}>
              <JobInstanceTaskListTable list={taskList} taskId={taskId} setTaskId={setTaskId} />
            </Col>
            <FlexDrawerArr
              iconTitleArr={[
                {icon: <IconSvg type="job-instance-params" />, title: '任务参数'},
                {icon: <IconSvg type="job-instance-result" />, title: '任务结果'},
                {
                  icon: <IconSvg type="job-instance-log" />,
                  title: '任务日志',
                  handleRefreshFn: () => {
                    setLogRefreshTime(new Date().getTime());
                  }
                }
              ]}
            >
              {/* 任务参数 */}
              <JobInstanceTaskParams jobInstanceTask={jobInstanceTask} />
              {/* 任务算子结果 */}
              <JobInstanceTaskOperation jobInstanceTask={jobInstanceTask} jobInstance={jobInstance} />
              {/* 任务日志 */}
              <JobInstanceTaskLog
                jobInstanceTask={jobInstanceTask}
                jobInstance={jobInstance}
                logRefreshTime={logRefreshTime}
              />
            </FlexDrawerArr>
          </Row>
        </div>
      </div>
    </div>
  );
};

export default JobInstanceResultPage;
