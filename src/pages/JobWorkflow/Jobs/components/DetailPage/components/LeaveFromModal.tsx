import {Button, Modal, Space} from 'acud';
import {useMemoizedFn} from 'ahooks';
import React, {useContext, useEffect, useState} from 'react';
import {useBlocker} from 'react-router-dom';
import styles from './index.module.less';
import IconSvg from '@components/IconSvg';
/**
 * 离开页面确认弹窗
 * isEditing 是否处于编辑状态
 * leaveClickTime 离开页面点击时间戳 如果大于0 则显示弹窗
 * name 工作流名称
 * onClickSave 保存并关闭
 */
interface ILeaveModalProps {
  leaveClickTime: number;
  isEditing: boolean;
  name: string;
  onClickAndRefresh: () => void;
  onSave: () => Promise<boolean>;
}
const LeaveFromModal: React.FC<ILeaveModalProps> = ({
  leaveClickTime,
  isEditing,
  name,
  onClickAndRefresh,
  onSave
}) => {
  const [visible, setVisible] = useState(false);

  // 监听页面是否处于编辑状态
  const blocker = useBlocker(
    ({currentLocation, nextLocation}) => isEditing && currentLocation.pathname !== nextLocation.pathname
  );
  // 监听页面是否处于编辑状态 如果处于编辑状态，则显示弹窗
  useEffect(() => {
    if (blocker.state === 'blocked') {
      setVisible(true);
    }
  }, [blocker]);
  // 点击x弹窗
  const closeModal = useMemoizedFn(() => {
    setVisible(false);
    if (blocker?.state === 'blocked') {
      blocker?.reset();
    }
  });
  // 取消弹窗
  const handleCancel = useMemoizedFn(() => {
    setVisible(false);
    if (blocker?.state === 'blocked') {
      blocker?.proceed();
    } else {
      onClickAndRefresh();
    }
  });

  // 保存并关闭
  const handleOk = useMemoizedFn(async () => {
    const res = await onSave();
    setVisible(false);
    // 如果保存成功 则关闭弹窗
    if (res) {
      if (blocker?.state === 'blocked') {
        blocker?.proceed();
      } else {
        onClickAndRefresh();
      }
    }
  });

  useEffect(() => {
    if (leaveClickTime) {
      setVisible(true);
    }
  }, [leaveClickTime]);
  return (
    <Modal
      title={
        <Space>
          <IconSvg type="warning" color="#FF9326" size={22} />
          <span>关闭确认</span>
        </Space>
      }
      className={styles['leave-modal']}
      width={400}
      visible={visible}
      onCancel={closeModal}
      footer={
        <Space>
          <Button onClick={handleCancel}>立即关闭</Button>
          <Button type="primary" onClick={handleOk}>
            保存并关闭
          </Button>
        </Space>
      }
    >
      当前工作流“{name}”暂未保存，如果现在关闭，未保存的数据内容将丢失。请确认是否要继续关闭
    </Modal>
  );
};

export default LeaveFromModal;
