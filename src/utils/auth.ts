import {EAuth} from '@pages/index';

interface IHasAccessProps {
  authKey: EAuth;
  globalMetastoreRead?: boolean;
}

/**
 * 判断是否拥有权限,若无authKey字段，则默认拥有权限
 *
 * @param authKey 权限key
 * @param globalMetastoreRead 是否拥有全局元数据读权限
 * @returns 是否拥有权限
 */
export function hasAccess({authKey, globalMetastoreRead}: IHasAccessProps): boolean {
  if (!authKey) {
    return true;
  }

  switch (authKey) {
    case EAuth.GlobalMetastoreRead:
      return !!globalMetastoreRead;
    default:
      return false;
  }
}
