.show-uploading-file-com {
  width: 420px;
  padding: 20px 10px;
  position: fixed;
  bottom: 0;
  right: 50px;
  max-height: 400px;
  overflow: auto;
  background-color: #fff;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2), 0 6px 6px rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  z-index: 1000;

  > header {
    display: flex;
    align-items: center;
    justify-content: space-between;

    h4 {
      font-size: 16px;
      font-weight: 500;
    }

    span {
      cursor: pointer;
    }
  }

  .upload-item-header{
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &-alert {
    margin-top: 16px;
  }

  &-progress {
    position: absolute;
    left: 0;
    bottom: 0;
  }

  .show-uploading-file-com-list {
    margin-top: 16px;

    .upload-item {
      margin-bottom: 8px;
      background: #FFFFFF;
      border: 1px solid #E8E9EB;
      border-radius: 6px;
      overflow: hidden;
      height: 32px;

      .upload-item-header {
        height: 20px;
        display: flex;
        align-items: center;
        padding: 6px 8px 4px;

        &-icon {
          margin-right: 8px;
        }

        &-btn {
          height: 20px;
          padding: 0;
        }
      }
    }
  }
}
