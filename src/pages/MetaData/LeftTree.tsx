import React, {
  forwardRef,
  useImperative<PERSON>andle,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState
} from 'react';
import {Refresh, Plus1} from '@baidu/xicon-react-bigdata';
import {Button, Loading, Search, Tooltip, Tree} from 'acud';
import IconSvg from '@components/IconSvg';
import {useAsyncEffect, usePrevious, useRequest} from 'ahooks';
import {throttle} from 'lodash';

import * as http from '@api/metaRequest';

import ModalCreateName from './components/ModalCreateName';

import {WorkspaceContext} from '../index';
import {IUrlStateHandler, EnumNodeType, CatalogType} from './index';
import {joinNodeKey, transformToacudTreeData} from './helper';
import TextEllipsis from '@components/TextEllipsisTooltip';
import {RULE} from '@utils/regs';

interface DataNode {
  title: string | React.ReactNode;
  key: string;
  isLeaf?: boolean;
  children?: DataNode[];
}

// 请求 catalog list
const httpCatalogFun = async (workspaceId) => {
  const res = await http.getCatalogList({workspaceId});
  const catalogList = res.result.catalogs;
  return catalogList || [];
};

// 请求 schema list
const httpScheamFun = async (workspaceId, catalogName) => {
  const schemaRes = await http.getSchemaList({
    workspaceId,
    catalogName
  });
  const schemaList = schemaRes.result.schemas;
  return schemaList || [];
};

// 请求 table list
const httpTableFun = async (workspaceId, catalogName, schemaName) => {
  const tableRes = await http.getTableList({
    workspaceId,
    catalogName,
    schemaName
  });
  const tableList = tableRes.result.tables;
  return tableList || [];
};

// 请求 volume list
const httpVolumeFun = async (workspaceId, catalogName, schemaName) => {
  const volumeRes = await http.getVolumeList({
    workspaceId,
    catalogName,
    schemaName
  });
  const volumeList = volumeRes.result.volumes;
  return volumeList || [];
};

// 请求 operator list
const httpOperatorFun = async (workspaceId, catalogName, schemaName) => {
  const operatorRes = await http.getOperatorList({
    workspaceId,
    catalogName,
    schemaName
  });
  const operatorList = operatorRes.result.operators;
  return operatorList || [];
};

// trans API 数据为 Tree 数据格式
const transApiData2TreeData = (arr) => {
  return arr.map((item) => ({
    title: (
      <TextEllipsis width={'calc(100% - 24px)'} tooltip={item}>
        {item}
      </TextEllipsis>
    ),
    key: item
  }));
};

// 更新对应 Tree Node 的 children
const updateTreeData = (list: DataNode[], key: React.Key, children: DataNode[]): DataNode[] =>
  list.map((node) => {
    if (node.key === key) {
      return {
        ...node,
        children
      };
    }
    if (node.children) {
      return {
        ...node,
        children: updateTreeData(node.children, key, children)
      };
    }
    return node;
  });

// Tree节点显示对应图标
export const iconSelectFun = (e) => {
  const str = e.data.key;
  const separator = '#>';
  let count = 0;
  let pos = str.indexOf(separator);
  let type = '';

  while (pos !== -1) {
    count++;
    const i = str.indexOf(separator, pos + separator.length);
    if (count == 2) {
      type = 'meta-' + str.slice(pos + 2, i);
    }
    pos = i;
  }
  // 通过 #> 定位是哪一层级节点
  switch (count) {
    case 0:
      return str === CatalogType.SYSTEM ? (
        <IconSvg type="meta-system" size={16} color="#6c6d70" />
      ) : (
        <IconSvg type="meta" size={16} color="#6c6d70" />
      );
    case 1:
      return <IconSvg type="meta-schema" size={16} color="#6c6d70" />;
    case 2:
      return null;
    case 3:
      return <IconSvg type={type} size={16} color="#6c6d70" />;
  }
};

const klass = 'meta-data-left-tree';

const LeftTree = (props: IUrlStateHandler, ref: any) => {
  // 获取工作空间ID
  const {workspaceId} = useContext(WorkspaceContext);

  const {urlState, changeUrlFun, changeUrlFunReplace, hasMetastore, canWrite} = props;
  const {catalog, schema, type, node} = urlState;

  const [treeData, setTreeData] = useState<any>([]);

  const [selectedKeys, setSelectedKeys] = useState<string[]>(() => []);
  const selectPreKey = usePrevious(selectedKeys);

  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  // 搜索值
  const [searchVal, setSearchVal] = useState<string>('');

  // 搜索值变更监听
  const onSearchChange = useCallback((e) => {
    setSearchVal(e.target.value);
  }, []);

  // 初始化 url 中未有 catalog 参数情况
  useAsyncEffect(async () => {
    if (!catalog) {
      const catalogList = await httpCatalogFun(workspaceId);
      setTreeData(transApiData2TreeData(catalogList));
      setSelectedKeys([CatalogType.SYSTEM]);
      changeUrlFunReplace({
        catalog: CatalogType.SYSTEM
      });
    }
  }, []);

  // 树节点搜索
  const {loading: treeLoading, run: onSearch} = useRequest(
    async (value) => {
      if (value) {
        const res = await http.searchMetastore({filter: value, workspaceId: workspaceId});
        const data = res?.result || [];
        const treeData = transformToacudTreeData(data, value);
        setTreeData(treeData);
      } else {
        onRefresh();
      }
    },
    {
      manual: true
    }
  );

  // 树选择
  const onTreeSelect = (Keys, info) => {
    // 取消选择时，不变化
    if (Keys.length === 0) {
      selectPreKey && setSelectedKeys(selectedKeys);
      return;
    }
    // 点击文件夹时不变化
    // 判断 Keys[0] 中包含两个 '#>'时，说明点击的是文件夹
    if (Keys[0].split('#>').length === 3) {
      setSelectedKeys(selectedKeys);
      const index = expandedKeys?.indexOf(Keys[0]);
      if (index > -1) {
        setExpandedKeys([...expandedKeys.slice(0, index), ...expandedKeys.slice(index + 1)]);
      } else {
        setExpandedKeys([...expandedKeys, Keys[0]]);
      }
      return;
    }

    setSelectedKeys(Keys);

    const selectKey = Keys[0] || '';
    const [catalog, schema, type, node] = selectKey.split('#>');

    // 当点击分类文件夹时，不更新 urlState
    if (type && !node) {
      return;
    }

    changeUrlFun({
      catalog,
      schema,
      type,
      node,
      tab: undefined,
      path: undefined
    });
  };

  // 树展开
  const onExpand = (expandedKeys) => {
    setExpandedKeys(expandedKeys);
  };

  // 异步加载 Tree Data
  const onLoadData = async ({key, children}: any) => {
    if (children) {
      return;
    }
    //根据 key 中 的 #> 分割出来判断第几层, 也用 #> 来拼接 key
    if (key.indexOf('#>') < 0) {
      // 请求 第二层 schema
      const schema = await httpScheamFun(workspaceId, key);
      setTreeData((origin) => {
        return updateTreeData(
          origin,
          key,
          schema.map((item) => {
            return {
              title: (
                <TextEllipsis
                  width={'calc(100% - 24px)'}
                  tooltip={item}
                  keyword={searchVal}
                  keywordColor="#2468F2"
                >
                  {item}
                </TextEllipsis>
              ),
              key: `${key}#>${item}`
            };
          })
        );
      });
    } else {
      const [catalogName, schemaName] = key.split('#>');

      // 使用 Promise.allSettled 处理多个异步请求
      const [tableResult, volumeResult, operatorResult] = await Promise.allSettled([
        httpTableFun(workspaceId, catalogName, schemaName),
        httpVolumeFun(workspaceId, catalogName, schemaName),
        httpOperatorFun(workspaceId, catalogName, schemaName)
      ]);

      // 处理每个请求的结果，如果失败则设置为空数组
      const tableList = tableResult.status === 'fulfilled' ? tableResult.value : [];
      const volumeList = volumeResult.status === 'fulfilled' ? volumeResult.value : [];
      const operatorList = operatorResult.status === 'fulfilled' ? operatorResult.value : [];

      setTreeData((origin) => {
        return updateTreeData(origin, key, [
          ...(tableList?.length
            ? [
                {
                  title: `Table${tableList?.length ? `（${tableList.length}）` : ''}`,
                  key: `${key}#>${EnumNodeType.TABLE}`,
                  children: tableList.map((item) => {
                    return {
                      title: (
                        <TextEllipsis
                          width={'calc(100% - 24px)'}
                          tooltip={item}
                          keyword={searchVal}
                          keywordColor="#2468F2"
                        >
                          {item}
                        </TextEllipsis>
                      ),
                      key: `${key}#>${EnumNodeType.TABLE}#>${item}`,
                      isLeaf: true
                    };
                  })
                }
              ]
            : []),
          ...(volumeList?.length
            ? [
                {
                  title: `Volume${volumeList?.length ? `（${volumeList.length}）` : ''}`,
                  key: `${key}#>${EnumNodeType.VOLUME}`,
                  children: volumeList.map((item) => {
                    return {
                      title: (
                        <TextEllipsis
                          width={'calc(100% - 24px)'}
                          tooltip={item}
                          keyword={searchVal}
                          keywordColor="#2468F2"
                        >
                          {item}
                        </TextEllipsis>
                      ),
                      key: `${key}#>${EnumNodeType.VOLUME}#>${item}`,
                      isLeaf: true
                    };
                  })
                }
              ]
            : []),
          ...(operatorList?.length
            ? [
                {
                  title: `Operator${operatorList?.length ? `（${operatorList.length}）` : ''}`,
                  key: `${key}#>${EnumNodeType.OPERATOR}`,
                  children: operatorList.map((item) => {
                    return {
                      title: (
                        <TextEllipsis
                          width={'calc(100% - 24px)'}
                          tooltip={item}
                          keyword={searchVal}
                          keywordColor="#2468F2"
                        >
                          {item}
                        </TextEllipsis>
                      ),
                      key: `${key}#>${EnumNodeType.OPERATOR}#>${item}`,
                      isLeaf: true
                    };
                  })
                }
              ]
            : [])
        ]);
      });
    }
  };

  // 根据 url 参数 加载 Tree 节点
  const requestTreeChildren = async () => {
    const catalogList = await httpCatalogFun(workspaceId);
    setTreeData(transApiData2TreeData(catalogList));
    if (schema) {
      await onLoadData({key: catalog});
      if (type && node) {
        await onLoadData({key: `${catalog}#>${schema}`});
      }
    }
  };

  // 根据 URL 来回显 TreeData 并 选中节点
  useAsyncEffect(async () => {
    // 更新 TreeData（仅页面初始化加载数据）
    if (catalog && !treeData.length) {
      await requestTreeChildren();
    }
    const nodeKeys = joinNodeKey(urlState);
    // 更新 Tree 选中的 selectKeys
    setSelectedKeys([nodeKeys.selectedKeys || CatalogType.SYSTEM]);
    // 更新 Tree 展开的节点 expandedKeys
    setExpandedKeys(Array.from(new Set([...expandedKeys, ...nodeKeys.expandedKeys])));
  }, [urlState]);

  // 重新加载
  const onRefresh = useCallback(
    throttle(async () => {
      // 先置空
      setTreeData([]);
      if (searchVal) {
        // 存在搜索条件，转给搜索逻辑处理
        onSearch('');
        return;
      }
      await requestTreeChildren();
      const nodeKeys = joinNodeKey(urlState);
      setSelectedKeys([nodeKeys.selectedKeys!]);
      setExpandedKeys([...nodeKeys.expandedKeys]);
    }, 1000),
    [searchVal, urlState]
  );

  // 新建 Catalog 弹窗
  const [visibleModal, setVisibleModal] = useState<boolean>(false);
  const createSuccessFun = useCallback(
    (formData: {name: string}) => {
      changeUrlFun({catalog: formData.name, schema: '', type: '', node: ''});
      onRefresh();
    },
    [changeUrlFun]
  );

  // 设置 Tree 树滚动范围最大高度
  const [maxHeight, setMaxHeight] = useState<string>();
  const leftBoxDomRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    const handleResize = () => {
      const height = leftBoxDomRef.current?.offsetHeight ?? 0;
      setMaxHeight(height ? `${height - 95}px` : '100%'); // 更新最大高度
    };
    handleResize();
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const treeProps = useMemo(
    () => ({
      ...(searchVal ? {} : {expandedKeys: expandedKeys}),
      defaultExpandAll: searchVal ? true : false
    }),
    [searchVal, expandedKeys]
  );

  useImperativeHandle(ref, () => ({
    refresh: onRefresh
  }));

  return (
    <div className={`${klass}`} ref={leftBoxDomRef}>
      <header className={`${klass}-header`}>
        <h3>元数据</h3>
        <div>
          <Button
            type="text"
            icon={<IconSvg type="refresh-2-arrow" color="#151b26" size={16} />}
            onClick={onRefresh}
          ></Button>
          <Tooltip placement="top" title={hasMetastore ? '新建Catalog' : '无法创建Catalog，请先配置元存储'}>
            <Button
              type="text"
              onClick={() => setVisibleModal(true)}
              disabled={!hasMetastore || !canWrite}
              icon={<Plus1 theme="line" color="#151b26" size={16} strokeLinejoin="round" />}
            ></Button>
          </Tooltip>
        </div>
      </header>
      <Search
        value={searchVal}
        onChange={onSearchChange}
        placeholder="请输入名称搜索"
        allowClear
        onSearch={onSearch}
      />
      <Loading loading={treeLoading}>
        <div className={`${klass}-content`} style={{maxHeight: maxHeight, overflowY: 'auto'}}>
          {treeLoading ? null : (
            <Tree
              treeData={treeData}
              selectedKeys={selectedKeys}
              {...treeProps}
              icon={iconSelectFun}
              onSelect={onTreeSelect}
              loadData={onLoadData}
              onExpand={onExpand}
            />
          )}
        </div>
      </Loading>
      {/** 创建 Catalog 弹窗 */}
      <ModalCreateName
        visible={visibleModal}
        setVisible={setVisibleModal}
        title="Catalog"
        requestFun={http.createCatalog}
        requestParams={{workspaceId}}
        successFun={createSuccessFun}
        nameRules={[
          {
            validator: async (_, value) => {
              // 校验特殊字符和长度限制
              if (!RULE.specialName64.test(value)) {
                return Promise.reject(new Error(RULE.specialName64Text));
              }
              // 异步校验Volume名称是否重复，复用查询接口 silent模式
              const res = await http.getCatalogDetail(`${value}`, true);
              if (res.success && res.result?.catalog?.id) {
                return Promise.reject(new Error('该Catalog名称已存在，请重新输入'));
              }
              return Promise.resolve();
            }
          }
        ]}
      />
    </div>
  );
};

export default forwardRef(LeftTree);
