import React from 'react';
import type {DataNode} from 'acud/es/tree';

interface treeNode<T> extends DataNode {
  businessData?: T;
}

// 更新树数据的辅助函数
export const updateTreeData = <T>(
  list: treeNode<T>[],
  key: React.Key,
  children: treeNode<T>[]
): treeNode<T>[] => {
  return list.map((node) => {
    if (node.key === key) {
      return {
        ...node,
        children
      };
    }
    if (node.children) {
      return {
        ...node,
        children: updateTreeData(node.children, key, children)
      };
    }
    return node;
  });
};
