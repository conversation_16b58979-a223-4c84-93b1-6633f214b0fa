/**
 * 私有化环境，全局下的顶部栏 (接替公有云 framework 能力)
 * 同 WorkspaceHeader 能力相似，但仅用于私有化并且在空间外使用
 * <AUTHOR>
 */
import {FC, useEffect, useMemo, useState} from 'react';
import {Menu, Select, Dropdown, Link} from 'acud';
import {queryWorkspaceList} from '@api/workspace';
import {useNavigate} from 'react-router-dom';
import urls from '@utils/urls';
import {useRegion} from '@hooks/useRegion';
import useUrlState from '@ahooksjs/use-url-state';
import cx from 'classnames';
import store from '@store/index';
import IconSvg from '@components/IconSvg';

import * as http from '@api/privateDeliver';

// 使用  WorkspaceHeader 下的头像下拉组件 & Less
import AvatarDropdown from '../WorkspaceHeader/AvatarDropdown';
import styles from '../WorkspaceHeader/index.module.less';

// 退出登录方法
import {accountLogout} from '@helpers/private-utils';

// 私有化暂无右信息栏
const infoList = [];

const RightMenu = ({items}: {items: Array<{label: string; key: string}>}) => (
  <Menu
    onClick={({key}: {key: string}) => {
      window.open(key, '_blank');
    }}
  >
    {items.map((item) => (
      <Menu.Item key={item.key}>{item.label}</Menu.Item>
    ))}
  </Menu>
);

const Header: FC = () => {
  const [userName, setUserName] = useState();

  const getUserInfo = async () => {
    const res = await http.getLoginUserInfo();
    const data = res.data;
    setUserName(data?.cookieInfo?.username);
  };

  // 从私有化门户获取  user/loginStatus 数据
  useEffect(() => {
    !userName && getUserInfo();
  }, [userName]);

  const navigate = useNavigate();

  // 标题信息
  const titleInfo = useMemo(
    () => ({
      title: (
        <div className={styles['header-title']} onClick={() => navigate('/manage-workspace')}>
          <div className={styles['header-title-icon']} />
        </div>
      )
    }),
    []
  );

  const menuList = useMemo(
    () => [
      {
        key: 'personalCenter',
        label: '个人中心',
        // FIXME: 暂时处理跳出去
        clickHandler: () => window.open(`${window.location.origin}/edap/#/personalCenter`, '_blank')
      }
    ],
    []
  );

  // 右侧信息栏
  const rightInfo = useMemo(() => {
    return (
      <>
        <div className={styles['right-info']}>
          <div className={styles['right-info-dropdown']}>
            {infoList.map((item) => (
              <div key={item.name} className={styles['right-info-dropdown-item']}>
                <Dropdown overlay={RightMenu({items: item.children})}>
                  <IconSvg type={item.icon} size={28}></IconSvg>
                </Dropdown>
              </div>
            ))}
          </div>
          <AvatarDropdown
            userName={decodeURIComponent(userName || '') || ''}
            header
            logout={accountLogout}
            menuList={menuList}
          />
        </div>
      </>
    );
  }, [menuList, userName]);

  return (
    <div className={cx(styles['workspace-header'], styles['db-workspace-header'])}>
      <Menu
        mode="horizontal"
        scope="global"
        className={styles['menu']}
        otherArea={rightInfo}
        titleInfo={titleInfo}
        logoInfo={{logo: undefined}}
      />
    </div>
  );
};

export default Header;
