import React, {useCallback, useContext, useEffect, useMemo, useState} from 'react';
import {useNavigate} from 'react-router-dom';
import {useRequest} from 'ahooks';
import {Button, Dropdown, Link, Menu, Modal, Pagination, Search, Space, Table, toast, Tooltip} from 'acud';
import {Plus1} from '@baidu/xicon-react-bigdata';

import RefreshButton from '@components/RefreshButton';
import {copyJob, deleteJob, IJob, queryJobList, startJob} from '@api/job';
import JobCreateModal from './components/CreateModal';

import styles from './index.module.less';
import urls from '@utils/urls';
import {OperateType} from '@utils/enums';
import {downloadStr} from '@utils/utils';
import JobEmptyPage from './components/EmptyPage';
import useUrlState from '@ahooksjs/use-url-state';
import IconSvg from '@components/IconSvg';
import {WorkspaceContext} from '@pages/index';
import {JobCreateType, JobDetailPageType, WorkflowPageCreateType} from '../constants';

const Jobs: React.FC = () => {
  const navigate = useNavigate();

  // 是否展示空页面 查询为空并且 总数为 0
  const [showCreatePage, setShowCreatePage] = useState(false);

  const [{create}, setUrlParams] = useUrlState();

  const {workspaceId} = useContext(WorkspaceContext);
  // 表格排序
  const [sorter, setSorter] = useState({});
  const [pagination, setPagination] = useState<{
    pageNo: number;
    pageSize?: number;
  }>({
    pageNo: 1
  });
  // 查询字段
  const [keyword, setKeyword] = useState('');
  // 默认支持名称查询
  const [keywordType] = useState('name');
  const [jobModal, setJobModal] = useState<{
    isVisible: boolean;
    jobType?: JobCreateType;
  }>({isVisible: false, jobType: JobCreateType.EMPTY});

  const {
    data: dataSource,
    loading,
    run: runQueryJobList
  } = useRequest(() => queryJobList({...pagination, sorter, keyword, keywordType}, workspaceId), {
    refreshDeps: [pagination, sorter], //  分页 和 排序变化时刷新
    onSuccess: (res) => {
      setShowCreatePage(res.result.total === 0 && !keyword);
    }
  });

  // 查询的时候 恢复到第一页
  const searchJob = () => {
    setPagination((obj) => ({...obj, pageNo: 1}));
  };
  // 工作流操作方法
  const jobFn = async (job: IJob, type: OperateType) => {
    switch (type) {
      case OperateType.RUN: {
        Modal.confirm({
          title: '运行确定',
          content: `确定要运行"${job.name}"？`,
          onOk() {
            return startJob(job.workspaceId, job.jobId).then((res) => {
              if (res.success) {
                toast.success({
                  message: '运行提交成功',
                  description: (
                    <span>
                      请前往运行记录查看结果，立即前往
                      <Link
                        className="global-notify-ticket-link cursor-pointer"
                        onClick={() => navigate(`${urls.jobResult}?jobInstanceId=${res.result}`)}
                      >
                        运行记录
                      </Link>
                    </span>
                  ),
                  duration: 5,
                  key: res.result.id
                });
                searchJob();
              } else {
                return Promise.reject();
              }
            });
          }
        });
        break;
      }
      case OperateType.COPY: {
        await copyJob(job, WorkflowPageCreateType.COPY);
        await searchJob();
        break;
      }
      case OperateType.EXPORT: {
        downloadStr(String(job.code), `${job.name}.json`);
        break;
      }
      case OperateType.EDIT: {
        // 导航到编辑页面
        navigate(`${urls.jobDetail}?jobId=${job.jobId}&edit=true`);
        break;
      }
      case OperateType.DETAIL: {
        // 导航到编辑页面
        navigate(`${urls.jobDetail}?jobId=${job.jobId}`);
        break;
      }
      case OperateType.LOG: {
        // 导航到实例列表
        navigate(`${urls.jobDetail}?jobId=${job.jobId}&type=${JobDetailPageType.JOB_INSTANCES}`);
        break;
      }
      case OperateType.DELETE: {
        Modal.confirm({
          title: '删除确定',
          content: `"${job.name}"删除后，工作流中的运行数据将被清空，无法恢复，请谨慎操作`,
          onOk() {
            // 删除工作流 按钮 loading
            return deleteJob(job.workspaceId!, job.jobId!).then((res) => {
              if (res.success) {
                toast.success({message: '删除成功', duration: 5});
                searchJob();
              } else {
                return Promise.reject();
              }
            });
          }
        });
        break;
      }

      default:
        break;
    }
  };

  const columns: any = [
    {
      title: '工作流名称',
      dataIndex: 'name',
      key: 'name',
      width: 180,
      fixed: 'left',
      ellipsis: {
        showTitle: false
      },
      render: (name, record) => (
        <Tooltip placement="topLeft" title={name}>
          <a onClick={() => jobFn(record, OperateType.DETAIL)}>{name}</a>
        </Tooltip>
      )
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',

      width: 300,
      ellipsis: {
        showTitle: false
      },
      render: (description) => (
        <Tooltip placement="topLeft" title={description}>
          {description || '-'}
        </Tooltip>
      )
    },
    {
      title: '创建人',
      dataIndex: 'createUser',
      key: 'createUser',
      width: 180
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 180,
      sorter: true
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      width: 180,
      sorter: true
    },

    {
      title: '操作',
      key: 'operate',
      width: 180,
      fixed: 'right',
      render: (record: IJob) => {
        return (
          <Space>
            <Link key={OperateType.RUN} onClick={() => jobFn(record, OperateType.RUN)}>
              运行
            </Link>
            <Link key={OperateType.EDIT} onClick={() => jobFn(record, OperateType.EDIT)}>
              编辑
            </Link>
            <Link key={OperateType.LOG} onClick={() => jobFn(record, OperateType.LOG)}>
              运行记录
            </Link>
            <Dropdown
              overlayStyle={{width: 100}}
              label={<IconSvg type="more" size={16} />}
              overlay={
                <Menu onClick={(e) => {}}>
                  <Menu.Item key={OperateType.COPY} onClick={() => jobFn(record, OperateType.COPY)}>
                    复制
                  </Menu.Item>
                  <Menu.Item key={OperateType.EXPORT} onClick={() => jobFn(record, OperateType.EXPORT)}>
                    导出
                  </Menu.Item>
                  <Menu.Item key={OperateType.DELETE} onClick={() => jobFn(record, OperateType.DELETE)}>
                    删除
                  </Menu.Item>
                </Menu>
              }
            />
          </Space>
        );
      }
    }
  ];

  const showTotal = useCallback(() => {
    return dataSource?.result?.total ? `共 ${dataSource?.result?.total} 条` : '';
  }, [dataSource?.result?.total]);

  // 处理表格变化（分页、排序、筛选）
  const handleTableChange = (pagination, filters, sorter) => {
    setSorter(sorter);
  };
  /**
   * 处理新建弹窗关闭
   * @param flag 是否刷新页面
   */
  const handleCreateJob = (flag: boolean) => {
    if (flag) {
      searchJob();
    }
    setUrlParams({create: undefined});
    setJobModal({isVisible: false});
  };

  useEffect(() => {
    // 如果有 create
    if (create) {
      if (create === JobCreateType.JSON) {
        setJobModal({isVisible: true, jobType: JobCreateType.JSON});
      } else {
        setJobModal({isVisible: true, jobType: JobCreateType.EMPTY});
      }
    }
  }, [create]);
  return (
    <div>
      {showCreatePage ? (
        <JobEmptyPage setJobModal={setJobModal} />
      ) : (
        <>
          <div className={styles['operation-container']}>
            <div className="w-[240px]">
              <Search
                placeholder="请输⼊⼯作流名称进⾏搜索"
                className={styles['search-container']}
                allowClear
                onSearch={searchJob}
                onChange={(e) => setKeyword(e.target.value)}
              />
            </div>
            <Space className={styles['right-container']}>
              <RefreshButton onClick={runQueryJobList}></RefreshButton>

              <Button
                onClick={() => {
                  setJobModal({isVisible: true, jobType: JobCreateType.JSON});
                }}
                icon={<IconSvg type="workflow-import" />}
                key="import"
              >
                导入工作流
              </Button>
              <Button
                type="primary"
                key="create"
                icon={<Plus1 theme="line" size={16} strokeLinejoin="round" />}
                onClick={() => {
                  setJobModal({isVisible: true, jobType: JobCreateType.EMPTY});
                }}
              >
                创建工作流
              </Button>
            </Space>
          </div>

          <Table
            scroll={{x: 1228}}
            dataSource={dataSource?.result?.jobs}
            columns={columns}
            rowKey="jobId"
            loading={{
              loading,
              size: 'small'
            }}
            pagination={false}
            onChange={handleTableChange}
          />

          <div className={styles['pagination-container']}>
            <Pagination
              showSizeChanger
              showQuickJumper
              current={pagination.pageNo}
              showTotal={showTotal}
              total={dataSource?.result?.total || 0}
              onChange={(page, pageSize) => {
                setPagination({
                  pageNo: page,
                  pageSize: pageSize
                });
              }}
            />
          </div>
        </>
      )}

      <JobCreateModal {...jobModal} onSubmit={handleCreateJob} />
    </div>
  );
};

export default Jobs;
