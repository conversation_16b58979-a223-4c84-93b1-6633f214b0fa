import useUrlState from '@ahooksjs/use-url-state';
import {deleteJob, detailJob, editJob, IJob, startJob} from '@api/job';
import FlexDrawerArr from '@components/FlexDrawerArr';
import MonacoPage from '@pages/JobWorkflow/Jobs/components/MonacoPage';
import {Breadcrumb, Button, Col, Form, Input, Link, Modal, Row, Space, Tabs, toast} from 'acud';
import {useMemoizedFn, useRequest} from 'ahooks';
import React, {useContext, useEffect, useState} from 'react';
import {Clipboard, Ellipsis} from '@baidu/bce-react-toolkit';
import {useBlocker, useLocation, useNavigate} from 'react-router-dom';
import X6Page from '../X6Page';
import styles from './index.module.less';
import {OperateType} from '@utils/enums';
import JobInstance from '@pages/JobInstance';
import urls from '@utils/urls';
import {RULE} from '@utils/regs';
import ImportantJsonModal from '../ImportantJson';
import IconSvg from '@components/IconSvg';
import {WorkspaceContext} from '@pages/index';
import {JobDetailPageType} from '@pages/JobWorkflow/constants';
import LeaveFromModal from './components/LeaveFromModal';

const JobDetailPage: React.FC = () => {
  const navigate = useNavigate();

  const [{jobId, edit}] = useUrlState();
  const {workspaceId} = useContext(WorkspaceContext);

  const [json, setJson] = useState('');
  // 表单是否修改
  const [formIsDirty, setFormIsDirty] = useState(false);
  const [leaveClickTime, setLeaveClickTime] = useState(0);
  const [activeIndex, setActiveIndex] = useState<number | undefined>(1);
  const [isEditing, setIsEditing] = useState(!!edit);
  const [form] = Form.useForm();
  // 保存工作流
  const saveJob = useMemoizedFn(async () => {
    let obj;
    try {
      // 校验基础信息
      obj = await form.validateFields();
    } catch (error) {
      console.log(error);
      setActiveIndex(0);
      return false;
    }

    try {
      const {success} = await editJob({
        ...obj,
        workspaceId,
        jobId,
        code: json
      });
      if (success) {
        toast.success({
          message: '保存成功',
          duration: 5
        });
        setIsEditing(false);
        setFormIsDirty(false);
        findJobDetail();
        setActiveIndex(1);
        return true;
      }
    } catch (error) {
      console.log(error);
    }
    return false;
  });

  // 区分 列表与模板页面
  const [state, setState] = useUrlState<{type: string}>({
    type: JobDetailPageType.DETAIL
  });

  // 切换 修改 url 参数 保证刷新也可以正常切换页面
  const onChange = (activeKey: string) => {
    setState({type: activeKey});
  };

  const initData = (jobDetail?: IJob) => {
    setJson(jobDetail?.code || '');
    form.setFieldsValue({
      name: jobDetail?.name,
      description: jobDetail?.description
    });
  };
  // 获取工作流详情
  const {
    data: jobDetail,
    loading,
    run: findJobDetail
  } = useRequest(() => detailJob(workspaceId, jobId), {
    onSuccess: (res) => {
      initData(res.result);
    }
  });

  // 操作工作流
  const dealJobFn = useMemoizedFn(async (type: OperateType) => {
    switch (type) {
      case OperateType.RUN:
        startJob(workspaceId, jobId).then((res) => {
          if (res.success) {
            toast.success({
              message: '运行提交成功',
              description: (
                <span>
                  请前往运行记录查看结果，立即前往
                  <Link
                    className="global-notify-ticket-link cursor-pointer"
                    onClick={() => navigate(`${urls.jobResult}?jobInstanceId=${res.result}`)}
                  >
                    运行记录
                  </Link>
                </span>
              ),
              duration: 5,
              key: res.result.id
            });
          }
        });
        break;
      case OperateType.EDIT:
        setIsEditing(true);
        setFormIsDirty(false);
        setActiveIndex(1);
        break;

      case OperateType.SAVE:
        saveJob();
        break;
      case OperateType.DELETE:
        Modal.confirm({
          title: '确定删除当前工作流作业吗？',
          content: `“${jobDetail.result.name}”删除后，工作流中的运行数据将被清空，无法恢复，请慎重`,
          onOk: () => {
            deleteJob(workspaceId, jobId).then((res) => {
              if (res.success) {
                toast.success({
                  message: '删除成功',
                  description: '删除成功',
                  duration: 5
                });
                navigate(urls.job);
              }
            });
          }
        });
        break;
      case OperateType.CANCEL:
        if (formIsDirty) {
          setLeaveClickTime(new Date().getTime());
        } else {
          setIsEditing(false);
          setFormIsDirty(false);
          findJobDetail();
        }
        break;
    }
  });

  // 关闭弹窗 flag 为 true 则保存
  const onClickAndRefresh = useMemoizedFn(async () => {
    // 关闭弹窗 不保存
    initData(jobDetail?.result);
    setIsEditing(false);
    setFormIsDirty(false);
  });
  return (
    <div className={styles['job-detail']}>
      <div className={styles['detail-page']}>
        {/* 顶部面包屑 + 标题 */}
        <div className={styles['header']}>
          <Breadcrumb>
            <Breadcrumb.Item>
              <Link onClick={() => navigate(urls.job)} className={styles['breadcrumb-link']}>
                工作流列表
              </Link>
            </Breadcrumb.Item>

            <Breadcrumb.Item> </Breadcrumb.Item>
          </Breadcrumb>
          <div className={styles['title']}>
            <div className="w-1/2">
              <Ellipsis tooltip={jobDetail?.result?.name}>{jobDetail?.result?.name}</Ellipsis>
            </div>
            {!isEditing && state.type === JobDetailPageType.DETAIL && (
              <Button type="primary" className="float-right" onClick={() => dealJobFn(OperateType.DELETE)}>
                删除工作流
              </Button>
            )}
          </div>
        </div>
        {/* 顶部 tab 切换 详情/运行记录 */}
        <Tabs onChange={onChange} activeKey={state.type}>
          <Tabs.TabPane tab={'工作流详情'} key={JobDetailPageType.DETAIL}></Tabs.TabPane>
          <Tabs.TabPane
            disabled={isEditing}
            tab={'运行记录'}
            key={JobDetailPageType.JOB_INSTANCES}
          ></Tabs.TabPane>
        </Tabs>

        {/* 详情/运行记录 内容 没放在tab 因为需要靠左右边上，放里面有间距 */}
        <div className={styles['container']}>
          {state.type === JobDetailPageType.DETAIL ? (
            // 内容整体布局分为左右两部分
            <Row className={styles['container-row']}>
              {/* 内容区域 */}
              <Col flex="1  1 0px" className={styles['container-left']}>
                {/* 操作按钮区域 */}
                <div className={styles['left-btn']}>
                  {!isEditing ? (
                    <>
                      <Button
                        size="small"
                        onClick={() => dealJobFn(OperateType.EDIT)}
                        icon={<IconSvg type="edit" />}
                        type="text"
                      >
                        编辑
                      </Button>
                      <Button
                        size="small"
                        onClick={() => dealJobFn(OperateType.RUN)}
                        icon={<IconSvg type="workflow-run" />}
                        type="text"
                      >
                        运行
                      </Button>
                    </>
                  ) : (
                    <>
                      <Button
                        size="small"
                        onClick={() => dealJobFn(OperateType.SAVE)}
                        icon={<IconSvg type="workflow-save" />}
                        type="text"
                      >
                        保存
                      </Button>
                      <Button
                        size="small"
                        onClick={() => dealJobFn(OperateType.CANCEL)}
                        icon={<IconSvg type="workflow-quit" />}
                        type="text"
                      >
                        退出
                      </Button>

                      <ImportantJsonModal onChange={setJson}>
                        <Button size="small" icon={<IconSvg type="workflow-import" />} type="text">
                          工作流导入
                        </Button>
                      </ImportantJsonModal>
                    </>
                  )}
                </div>
                <div className={styles['monaco']}>
                  <MonacoPage
                    onChange={(value) => {
                      setJson(value);
                      setFormIsDirty(true);
                    }}
                    value={json}
                    readOnly={!isEditing}
                  />
                </div>
              </Col>
              {/* 右侧 作业基本信息 + 可视化预览 */}
              <FlexDrawerArr
                activeIndex={activeIndex}
                changeIndex={setActiveIndex}
                changeVisible={(flag) => {
                  if (!flag) {
                    setActiveIndex(undefined);
                  }
                }}
                iconTitleArr={[
                  {icon: <IconSvg type="workflow-detail" />, title: '作业基本信息', forceRender: true},
                  {icon: <IconSvg type="workflow-x6" />, title: '可视化预览'}
                ]}
              >
                <div>
                  <Form
                    className={styles['form-container']}
                    labelAlign="left"
                    layout="horizontal"
                    name="basic"
                    form={form}
                    labelWidth={70}
                    onValuesChange={(changedValues, allValues) => {
                      setFormIsDirty(true);
                    }}
                  >
                    {isEditing ? (
                      <Form.Item
                        label="工作流名称："
                        name="name"
                        rules={[
                          {required: true, message: '请输入工作流名称'},
                          {pattern: RULE.workflowName, message: RULE.workflowNameText}
                        ]}
                      >
                        <Input forbidIfLimit={true} limitLength={256} placeholder="请输入描述" allowClear />
                      </Form.Item>
                    ) : (
                      <Form.Item label="工作流名称：">{jobDetail?.result?.name || '-'}</Form.Item>
                    )}
                    <Form.Item label="工作流 ID：">
                      {jobDetail?.result?.jobId}
                      <Clipboard text={jobDetail?.result?.jobId} className={'inline-block'}>
                        <Button icon={<IconSvg type="copy" size={14} />} type="actiontext" />
                      </Clipboard>
                    </Form.Item>
                    <Form.Item label="创建时间">{jobDetail?.result?.createdAt}</Form.Item>
                    <Form.Item label="修改时间">{jobDetail?.result?.updatedAt || '-'}</Form.Item>
                    <Form.Item label="创建人">{jobDetail?.result?.createUser || '-'}</Form.Item>
                    <Form.Item label="最后修改人">{jobDetail?.result?.updateUser || '-'}</Form.Item>
                    {isEditing ? (
                      <Form.Item label="描述：" name="description">
                        <Input.TextArea
                          forbidIfLimit={true}
                          limitLength={500}
                          placeholder="请输入描述"
                          allowClear
                        />
                      </Form.Item>
                    ) : (
                      <Form.Item label="描述：">{jobDetail?.result?.description || '-'}</Form.Item>
                    )}
                  </Form>
                </div>
                {/* <div></div> */}
                {<X6Page json={json} />}
              </FlexDrawerArr>
            </Row>
          ) : (
            <div className={styles['job-instance-container']}>
              <JobInstance jobId={jobId} jobName={jobDetail?.result?.name} />
            </div>
          )}
        </div>
      </div>
      <LeaveFromModal
        leaveClickTime={leaveClickTime}
        isEditing={isEditing && formIsDirty}
        name={jobDetail?.result?.name}
        onClickAndRefresh={onClickAndRefresh}
        onSave={saveJob}
      />
    </div>
  );
};

export default JobDetailPage;
