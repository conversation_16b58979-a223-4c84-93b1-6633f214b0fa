.template-container {
  .operation-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;

    .total-count {
      color: #84868c;
    }

    .right-container {
      display: flex;
      align-items: flex-start;
      gap: 10px;
      .search-container {
        width: 240px;
      }
    }
  }

  .loading-container {
    min-height: 300px;
    width: 100%;
  }

  .templates-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(293px, 1fr));
    column-gap: 16px;
    row-gap: 16px;
    // 避免 box-shadow 无法展示
    padding: 0 2px;

    .template-item {
      height: 260px;
      border-radius: 4px;
      position: relative;
      border: 1px solid #E8E9EB;

      overflow: hidden;
      &-header {
        background-image: linear-gradient(180deg, #eff5ff 13%, #ffffff 100%);
        height: 56px;
        font-size: 16px;
        color: #151b26;
        line-height: 24px;
        padding: 16px 19px;
        font-weight: 500;
        .icon {
          vertical-align: top;
          margin-right: 8px;
        }
      }
      &-img {
        padding: 0 16px 16px;
        overflow: hidden;
        &-bg {
          height: 188px;
          width: 100%;
          border-radius: 6px;
          background-size: cover; /* 保持比例填充，裁剪左右 */
          background-position: center; /* 水平居中 */
          background-repeat: no-repeat;
        }
      }

      &-container {
        position: absolute;
        bottom: -130px;
        left: 0;
        right: 0;
        background-color: #fff;
        padding: 10px;
        border-radius: 0 0 4px 4px;
        transition: bottom 0.3s ease;

        &-description {
          overflow: hidden;
          -webkit-line-clamp: 2;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          min-height: 40px;
          line-height: 20px;
          margin-bottom: 10px;
        }

        &-options {
          padding: 0 6px;
          :global {
            .acud-btn {
              margin-right: 16px;
              width: 100%;

              &:last-child {
                margin-right: 0;
              }
            }
          }
        }
      }

      &:hover {
        // border-color: #000000;
        box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.12);
        .template-item-container {
          bottom: 0px;
        }
      }
    }
  }

  .pagination-container {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;

    :global {
      .acud-pagination {
        width: fit-content;
      }
    }
  }
}
