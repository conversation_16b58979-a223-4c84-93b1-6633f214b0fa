import MetaBreadcrumb from './components/MetaBreadcrumb';

import PanelCatalog from './partial/PanelCatalog';
import PanelSchema from './partial/PanelSchema';
import PanelTable from './partial/PanelTable';
import PanelVolume from './partial/PanelVolume';
import PanelOperator from './partial/PanelOperator';

import {IUrlStateHandler} from './index';
import {determineNodeType} from './helper';
import {Loading} from 'acud';

// 通过 URL 参数判断使用哪个 Panel 面板组件
const whichPanelComponents = {
  catalog: PanelCatalog, // Catalog 面板
  schema: PanelSchema,
  table: PanelTable,
  volume: PanelVolume,
  operator: PanelOperator
};

const klass = 'meta-data-right-content';

const RightContent = (props: IUrlStateHandler) => {
  const {urlState, changeUrlFun, changeUrlFunReplace, handleTreeRefresh, userList, canWrite} = props;

  const nodeType = determineNodeType(urlState);
  const PanelComponent = nodeType ? whichPanelComponents[nodeType] : null;

  return (
    <div className={`${klass}`}>
      {/* 面包屑导航 */}
      <MetaBreadcrumb urlState={urlState} changeUrlFun={changeUrlFun} />
      {/* 渲染某一个 Panel*/}
      {PanelComponent ? (
        <PanelComponent
          urlState={urlState}
          changeUrlFun={changeUrlFun}
          changeUrlFunReplace={changeUrlFunReplace}
          handleTreeRefresh={handleTreeRefresh}
          userList={userList}
          canWrite={canWrite}
        />
      ) : (
        <Loading loading>
          <div style={{height: 'calc(100vh - 150px)'}} />
        </Loading>
      )}
    </div>
  );
};

export default RightContent;
