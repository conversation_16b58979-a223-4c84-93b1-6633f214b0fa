// TODO: 后面根据实际开发 定工作空间前缀
export const WORKSPACE_ROUTER_PREFIX = '/workspace';

const urls = {
  /** 开通页 */
  activation: '/activation',
  /** 多模态-工作空间管理 */
  manageWorkspace: '/manage-workspace',
  /** 结构化-工作空间管理 */
  manageEDAPWorkspace: '/edap-workspace',
  /** 多模态-工作空间管理 - 详情 */
  manageWorkspaceDetail: '/manage-workspace/detail',
  /** 元存储 */
  metastore: '/metastore',
  /** MetaData */
  metaData: '/workspace/meta/data',
  /** 工作区 */
  workArea: `${WORKSPACE_ROUTER_PREFIX}/workarea`,
  /** 计算资源 */
  compute: `${WORKSPACE_ROUTER_PREFIX}/compute`,
  /** 计算资源创建 */
  computeCreate: `${WORKSPACE_ROUTER_PREFIX}/compute/create`,
  /** 工作流 */
  job: `${WORKSPACE_ROUTER_PREFIX}/job`,
  /** 工作流详情页 */
  jobDetail: `${WORKSPACE_ROUTER_PREFIX}/job/detail`,
  /** 运行记录 */
  jobInstance: `${WORKSPACE_ROUTER_PREFIX}/job/instance`,
  /** 模板 */
  templateDetail: `${WORKSPACE_ROUTER_PREFIX}/template/detail`,
  /** 运行记录结果 */
  jobResult: `${WORKSPACE_ROUTER_PREFIX}/job/result`,
  /** 运行记录结果 实例高亮 */
  jobInstanceResult: `${WORKSPACE_ROUTER_PREFIX}/job/instance/result`,
  /** 数据接入 */
  dataIngestion: `${WORKSPACE_ROUTER_PREFIX}/data-ingestion`
};

export default urls;
