.drawer-drag-container {
  width: 1px;
  background-color: #e1e2e4;
  height: 100%;
  position: absolute;
}

.drawer-draggable {
  cursor: col-resize;
  -webkit-user-drag: none;
  z-index: 999;

  &:hover {
    background-color: #2468f2;
    width: 4px;
  }
}

.drawer-left {
  position: relative;
  flex: 0 0 400px;
  overflow: hidden;
  height: 100%;

  flex-direction: column;

  .drawer-container {
    height: 100%;
    display: flex;
    flex-direction: column;

    .drawer-title {
      height: 40px;
      font-size: 14px;
      font-weight: 600;
      padding: 8px 4px 8px 16px;
      display: flex;
      align-items: center;
      border-bottom: 1px solid #e1e2e4;

      &-right-options {
        margin-left: auto;
      }
    }
    .drawer-children {
      flex: 1;
      overflow: auto;
    }
  }
}
.drawer-right {
  flex: 0 0 40px;
  // background-color: #fbfbfc;
  border-left: 1px solid #e1e2e4;
  padding: 8px;
  .drawer-right-btn-active {
    background: rgba(7, 12, 20, 0.06);
    border-radius: 4px;
    color: #151b26;
  }
}
