.compute-wrapper {
  padding: 0 8px 8px 8px;

  a {
    color: #2468F2;
  }

  .compute-content {
    position: relative;
    background: #ffffff;
    border: 1px solid #d4d6d9;
    border-radius: 6px;
    padding: 16px;
  }

  .compute-title {
    font-size: 20px;
    color: #151b26;
    font-weight: bold;
    margin-bottom: 20px;

    .compute-title-icon {
      transform: rotate(180deg);
      cursor: pointer;
      margin-right: 12px;
    }
  }

  .order-info {
    position: absolute;
    top: 64px;
    right: 22px;
    width: 320px;
    padding: 20px;
    background: #f7f7f9;
    border: 1px solid #eceef1;
    border-radius: 8px;

    .order-info-title {
      font-size: 16px;
      color: #191b26;
      line-height: 24px;
      font-weight: bold;
      margin-bottom: 16px;
    }

    .order-info-content {
      border-bottom: 1px solid #d4d6d9;
      padding-bottom: 16px;
      margin-bottom: 28px;
      .order-info-content-item {
        margin-bottom: 16px;

        .order-info-content-item-label {
          font-size: 12px;
          color: #191b26;
          line-height: 20px;
          font-weight: bold;
          display: inline-block;
          width: 48px;
          margin-right: 64px;
        }

        .order-info-content-item-value {
          font-size: 12px;
          color: #151b26;
          line-height: 20px;
        }
      }
    }

    .order-info-price {
      margin-bottom: 16px;
      .order-info-price-title {
        font-size: 16px;
        color: #191b26;
        line-height: 24px;
        font-weight: bold;
        margin-bottom: 16px;
      }

      .order-info-price-value {
        font-size: 32px;
        color: #f3503e;
        line-height: 45px;

        .symbol {
          font-size: 16px;
          line-height: 28px;
        }
      }
    }

    .order-info-price-service-agreement {
      margin-bottom: 16px;
    }

    .order-info-button {
      width: 100%;
    }
  }

  .form-wrapper {
    padding: 0 8px;
    .margin-bottom-40px {
      margin-bottom: 40px;
    }
  }
}

.legend-title {
  align-items: center;
  color: #151b26;
  display: flex;
  font-size: 16px;
  font-weight: 500;
  min-height: 24px;
  margin-bottom: 20px;
  &::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 14px;
    background: #2468F2;
    border-radius: 0 2px 2px 0;
    transform: scaleX(-1) scaleY(-1);
    position: relative;
    margin-right: 8px;
  }
}

.combine-zone-subnet {
  .zone-select {
    width: 150px;
    margin-right: 8px;

    :global {
      .acud-select {
        width: 100%;
      }
    }
  }

  :global {
    .acud-form-item {
      margin-bottom: 0;
    }
  }
}
