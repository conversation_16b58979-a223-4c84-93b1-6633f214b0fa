/**
 * 引入svg 需要整体替换 颜色 和 大小，否则无法修改大小颜色，执行 npm run svg进行压缩替换
 */

import React, {useEffect, useState} from 'react';
import Icon from 'acud-icon';
/**
 * 自定义 图标配置 使用方式 <IconSvg type="workflow-import" />
 * type 为 图片文件名称 @assets/svg/${type}.svg
 * size 为 图片大小 默认 1em，可以传递 16px 等单位
 * color 为 图片颜色
 */
interface IconSvgProps {
  type: string;
  size?: number;
  color?: string;
  className?: string;
  onClick?: () => void;
}
const IconSvg: React.FC<IconSvgProps> = ({type, size = '1em', color, ...props}) => {
  const [icon, setIcon] = useState<any | null>(null);

  useEffect(() => {
    import(`@assets/svg/${type}.svg`)
      .then((module) => {
        setIcon(() => module.default);
      })
      .catch(() => console.error(`Icon ${type} not found`));
  }, [type]);

  if (!icon) return null;

  return <Icon {...props} style={color ? {color: color} : {}} width={size} height={size} component={icon} />;
};

export default IconSvg;
