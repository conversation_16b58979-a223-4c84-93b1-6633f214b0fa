import {NodeView} from '@antv/x6';
import {Ellipsis} from '@baidu/bce-react-toolkit';
import {JobTaskType} from '@pages/JobWorkflow/constants';
import React, {useEffect, useRef, useState} from 'react';
import {Graph, Node} from '@antv/x6';
import IconSvg from '@components/IconSvg';
import {Input} from 'acud';
import {useMemoizedFn} from 'ahooks';
export class SimpleNodeTaskView extends NodeView {
  protected renderMarkup() {
    return this.renderJSONMarkup({
      tagName: 'rect',
      selector: 'body'
    });
  }

  update() {
    super.update({
      body: {
        refWidth: '100%',
        refHeight: '100%',
        fill: '#E8E9EB'
      }
    });
  }
}

export class SimpleNodeOperatorView extends NodeView {
  protected renderMarkup() {
    return this.renderJSONMarkup({
      tagName: 'rect',
      selector: 'body'
    });
  }

  update() {
    super.update({
      body: {
        refWidth: '100%',
        refHeight: '100%',
        fill: '#fff'
      }
    });
  }
}

/**
 * 任务节点组件
 * @param node 传递节点参数
 */
export const TaskNodeComponent = ({node}: {node: Node}): React.ReactNode => {
  const {name, type} = node.getData();
  return (
    <div className={'task-node'}>
      <div className={`task-node-title ${type === JobTaskType.DATAFLOW_TASK ? 'dataflow-task' : 'ray-task'}`}>
        <IconSvg
          className="task-node-title-icon"
          size={28}
          type={type === JobTaskType.DATAFLOW_TASK ? 'workflow-task-data-flow' : 'workflow-task-ray'}
        />
        <div className="task-node-title-name">
          <Ellipsis tooltip={name}>{name}</Ellipsis>
        </div>
      </div>
    </div>
  );
};

/**
 * 算子节点组件
 * @param node 节点对象
 */
export const OperatorNodeComponent = ({node}: {node: Node}): React.ReactNode => {
  const {name} = node.getData();
  return (
    <div className="operator-node">
      <Ellipsis tooltip={name}>{name}</Ellipsis>
    </div>
  );
};

// 最大放大倍数
const max = 500;
// 最小放大倍数
const min = 1;
// 默认放大倍数
const normal = 100;
// 处理字符串转换为数字
const dealStringToNumber = (numStr: string): number => {
  const match = numStr.match(/^(-?\d+(\.\d+)?)/); // 匹配开头的整数或小数
  return Number(match ? match[0] : normal);
};
// 处理最大最小值
const dealMaxMin = (numStr: string | number): number => {
  const num = dealStringToNumber(String(numStr));

  if (num > max) {
    return max;
  }
  if (num <= min) {
    return min;
  }
  return Math.round(num);
};
/**
 * 缩放组件
 * @param props 组件参数
 */
const ZoomComponentFn = (props: {onChange: (value: number) => void; value: number}) => {
  const [value, setValue] = useState<string>();

  // 组件值变化
  useEffect(() => {
    setValue(dealMaxMin(props.value) + '%');
    console.log(props.value);
  }, [props.value]);

  // 处理值加百分号
  const dealValue = (num: string | number) => {
    return num + '%';
  };

  // 输入框内容变化
  const onChange = useMemoizedFn((e: React.ChangeEvent<HTMLInputElement>) => {
    setValue(e.target.value);
  });

  // 失去焦点 处理输入框内容
  const onBlur = useMemoizedFn(() => {
    const num = dealMaxMin(value);
    props.onChange(num);
    setValue(dealValue(num));
  });

  // 点击放大缩小图标
  const onClickIcon = useMemoizedFn((num: number) => {
    const newNum = dealMaxMin(String(dealStringToNumber(value) + num));
    setValue(dealValue(newNum));
    props.onChange(newNum);
  });

  return (
    <Input
      size="large"
      className="w-full"
      prefix={
        <IconSvg
          className="cursor-pointer"
          onClick={() => onClickIcon(-5)}
          type="workflow-zoom-in"
          size={16}
        />
      }
      suffix={
        <IconSvg
          className="cursor-pointer"
          onClick={() => onClickIcon(5)}
          type="workflow-zoom-out"
          size={16}
        />
      }
      value={value}
      onBlur={onBlur}
      onChange={onChange}
    />
  );
};

export const ZoomComponent = React.memo(ZoomComponentFn);
