import React, {forwardRef, useImperativeHandle, useState} from 'react';
import {Modal, Input, Form} from 'acud';
import {renameFile, renameFolder} from '@api/WorkArea';
import type {GetWorkspaceFileListResult} from '@api/WorkArea';
import {useRequest} from 'ahooks';
import {FOLDER_LIMIT_LENGTH, FOLDER_NAME_ERROR_MESSAGE, FOLDER_NAME_REGEX} from '@pages/WorkArea/config';
import {validatePath, FILE_NAME_ERROR_MESSAGE} from '@utils/utils';

interface RenameModalProps {
  workspaceId: string;
  onSuccess?: (record: GetWorkspaceFileListResult) => void;
}

export interface RenameModalRef {
  open: (record: GetWorkspaceFileListResult) => void;
  close: () => void;
}

const RenameModal = forwardRef<RenameModalRef, RenameModalProps>((props, ref) => {
  const [visible, setVisible] = useState(false);
  const [record, setRecord] = useState<GetWorkspaceFileListResult | null>(null);
  const [form] = Form.useForm();

  const {run: rename, loading} = useRequest(
    ({name, workspaceId, id}) => {
      const api = {
        FILE: renameFile,
        FOLDER: renameFolder
      }[record?.nodeType || 'FILE'];
      return api({name, workspaceId, id});
    },
    {
      manual: true,
      onSuccess: () => {
        props.onSuccess?.(record!);
        handleClose();
      },
      onError: (error) => {
        console.error('重命名失败:', error);
      }
    }
  );

  useImperativeHandle(ref, () => ({
    open: (record: GetWorkspaceFileListResult) => {
      setVisible(true);
      setRecord(record);
      form.setFieldsValue({name: record.name});
    },
    close: handleClose
  }));

  const handleConfirm = async () => {
    await form.validateFields();
    const name = form.getFieldValue('name');
    await rename({
      name,
      workspaceId: props.workspaceId,
      id: record?.id || ''
    });
  };

  const nameRules = [
    {required: true, message: '请输入名称'},
    {
      validator: (_, value) => {
        if (!FOLDER_NAME_REGEX.test(value)) {
          return Promise.reject(new Error(FOLDER_NAME_ERROR_MESSAGE));
        }
        return Promise.resolve();
      }
    }
  ];

  const fileNameRules = [
    {
      validator: (_, value) => {
        if (!validatePath(value)) {
          return Promise.reject(new Error(FILE_NAME_ERROR_MESSAGE));
        }
        return Promise.resolve();
      }
    }
  ];

  const handleClose = () => {
    setVisible(false);
    form.resetFields();
  };

  const type = record?.nodeType === 'FOLDER' ? 'FOLDER' : 'FILE';

  return (
    <Modal
      title="修改名称"
      visible={visible}
      onOk={handleConfirm}
      onCancel={handleClose}
      confirmLoading={loading}
      destroyOnClose
    >
      <Form form={form} layout="vertical" inputMaxWidth="100%">
        {type === 'FOLDER' ? (
          <Form.Item name="name" label="文件夹名称" rules={nameRules} extra={FOLDER_NAME_ERROR_MESSAGE}>
            <Input limitLength={FOLDER_LIMIT_LENGTH} forbidIfLimit placeholder="请输入名称" />
          </Form.Item>
        ) : (
          <Form.Item
            required
            name="name"
            label="文件名称"
            rules={fileNameRules}
            extra={FILE_NAME_ERROR_MESSAGE}
          >
            <Input placeholder="请输入名称" />
          </Form.Item>
        )}
      </Form>
    </Modal>
  );
});

RenameModal.displayName = 'RenameModal';

export default RenameModal;
