/**
 * MetaTabs组件 - Panel的 tab 导航
 * <AUTHOR>
 */

import {useEffect, useState} from 'react';
import {Tabs} from 'acud';

const {TabPane} = Tabs;

interface IMetaTabsProps {
  panesList: {tab: string; key: string}[]; // TabPane 的列表
  tab?: string; // 选中的 tab
  onTabChange: (activeKey: string) => void; // tab 点击切换的事件
}

const MetaTabs = (props: IMetaTabsProps) => {
  const {panesList, tab = panesList[0].key, onTabChange} = props;
  const [activeKey, setActiveKey] = useState(tab);

  const onChange = (activeKey) => {
    setActiveKey(activeKey);
    onTabChange(activeKey);
  };

  useEffect(() => {
    setActiveKey(tab);
  }, [tab]);

  return (
    <div>
      <Tabs onChange={onChange} activeKey={activeKey}>
        {panesList.map((pane) => (
          <TabPane tab={pane.tab} key={pane.key}></TabPane>
        ))}
      </Tabs>
    </div>
  );
};

export default MetaTabs;
