import {PermissionType} from '@api/auth';
import {createSlice} from '@reduxjs/toolkit';

export interface IWorkspaceAuthState {
  catalog: PermissionType;
  compute: PermissionType;
  file: PermissionType;
  job: PermissionType;
  schema: PermissionType;
  table: PermissionType;
  volume: PermissionType;
  workflow: PermissionType;
  metastore: PermissionType;
  workspace: PermissionType;
}

const initialState: IWorkspaceAuthState = {
  catalog: undefined,
  compute: undefined,
  file: undefined,
  job: undefined,
  schema: undefined,
  table: undefined,
  volume: undefined,
  workflow: undefined,
  metastore: undefined,
  workspace: undefined
};

const workspaceAuthSlice = createSlice({
  name: 'workspaceAuth',
  initialState,
  reducers: {
    updateWorkspaceAuth: (state, action) => {
      [
        'catalog',
        'compute',
        'file',
        'job',
        'schema',
        'table',
        'volume',
        'workflow',
        'metastore',
        'workspace'
      ].forEach((key) => {
        state[key] = action.payload[key]?.[0];
      });
    }
  }
});

export const {updateWorkspaceAuth} = workspaceAuthSlice.actions;

export default workspaceAuthSlice.reducer;
