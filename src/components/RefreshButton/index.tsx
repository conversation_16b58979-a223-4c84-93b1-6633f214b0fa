import React from 'react';
import IconSvg from '@components/IconSvg';
import {Button} from 'acud';

interface RefreshButtonProps {
  onClick?: () => void;
  style?: React.CSSProperties;
  className?: string;
}

const RefreshButton: React.FC<RefreshButtonProps> = ({
  onClick,
  style,
  className
}) => {
  return (
    <Button
      icon={<IconSvg type="refresh" size={16} />}
      onClick={onClick}
      style={style}
      className={className}
    ></Button>
  );
};

export default RefreshButton;
