{"compilerOptions": {"baseUrl": "./", "paths": {"@pages/*": ["src/pages/*"], "@components/*": ["src/components/*"], "@hooks/*": ["src/hooks/*"], "@utils/*": ["src/utils/*"], "@api/*": ["src/api/*"], "@assets/*": ["src/assets/*"], "@styles/*": ["src/styles/*"], "@store/*": ["src/store/*"], "@type/*": ["src/type/*"], "@helpers/*": ["src/helpers/*"], "@/*": ["src/*"]}, "types": ["@baidu/bce-react-toolkit/es/types/global.d.ts"], "target": "ES5", "lib": ["DOM", "ES2022"], "jsx": "react-jsx", "strict": true, "strictNullChecks": false, "moduleResolution": "node", "esModuleInterop": true, "allowUmdGlobalAccess": true, "noImplicitAny": false, "experimentalDecorators": true}, "exclude": ["node_modules"]}