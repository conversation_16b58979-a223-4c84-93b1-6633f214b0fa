.workspace-permission-tab {
  width: 100%;
  height: 100%;
  position: relative;

  :global {
    .acud-table-wrapper .acud-table-empty {
      min-height: 60vh;
    }

    .acud-table-cell-ellipsis {
      .toolkit-ellipsis {
        width: 90%;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
      }
    }

    .acud-empty {
      min-height: 50vh;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-flow: column;
    }
  }

  .admin-tag,
  .user-tag {
    border-radius: 10px;
    line-height: 20px;
    padding-inline: 8px;
  }

  .admin-tag {
    background: #e6fffd;
    color: #00454d;
  }

  .user-tag {
    background: #e6f0ff;
    color: #144bcc;
  }

  .admin-empty-container {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    .admin-empty-block {
      display: flex;
      flex-flow: column;
      align-items: center;
      justify-content: center;
      background-color: #f7f6f6;
      width: 500px;
      height: 250px;
      border-radius: 8px;
      margin-top: -100px;

      .admin-empty-block-avatar {
        width: 50px;
        height: 50px;
        background-color: lightgrey;
      }
    }
  }

  .operation-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;

    .left-container {
      display: flex;
      align-items: center;
      gap: 8px;

      .search-container {
        width: 240px;
      }

      .total-count {
        color: #5c5f66;
      }
    }

    .right-container {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  .table-container {
    flex: 1;

    .table-operations {
      button {
        font-family: PingFangSC-Regular;
        padding: 0;
        min-width: unset;
      }
    }
  }

  .pagination-container {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;

    :global {
      .acud-pagination {
        width: fit-content;
      }
    }
  }
}
