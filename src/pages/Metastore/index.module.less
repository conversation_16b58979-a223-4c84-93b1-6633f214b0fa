.metastore-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  background-color: #fff;
  padding: 16px;
  box-sizing: border-box;
  overflow-y: auto;

  &-header {
    margin-bottom: 16px;
    font-family: PingFangSC-Medium;
    font-size: 18px;
    color: #151b26;
    line-height: 32px;
  }

  &-form {
    &-operation-btns {
      margin-top: 12px;
      padding-left: 138px;

      button {
        margin-right: 16px;
      }
    }

    &-storageLocation-disabled {
      :global {
        .bos-bucket-select {
          pointer-events: none;

          .bos-bucket-select-breadcrumb {
            border-color: #e8e9eb;
            color: #5c5f66;
            background-color: #f7f7f9;
          }
        }
      }
    }

    :global {
      .acud-form-item {
        margin-bottom: 20px;

        .acud-form-item-label {
          font-family: PingFangSC-Medium;
          font-size: 12px;
          color: #151b26;
          line-height: 20px;
          padding-left: 0;
        }
      }
    }
  }
}
