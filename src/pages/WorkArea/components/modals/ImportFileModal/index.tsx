import React, {forwardRef, useImperativeHandle, useRef, useState, useEffect} from 'react';
import _ from 'lodash';
import {Modal, toast, Upload, Form, Input} from 'acud';
import {GetWorkspaceFolderListResult, uploadFile} from '@api/WorkArea';
import type {UploadProps} from 'acud/lib/upload';
import ShowUploadFile, {ShowUploadFileRefHandle} from '@components/ShowUploadFile';
import styles from './index.module.less';
import classNames from 'classnames/bind';
import {useDrop} from 'ahooks';
import {TaskQueue} from '@utils/taskQueue';
import {validatePath, FILE_NAME_ERROR_MESSAGE} from '@utils/utils';

const cx = classNames.bind(styles);

interface ImportFileModalProps {
  onSuccess?: () => void;
  workspaceId?: string;
}

export interface ImportFileModalRef {
  open: (record: GetWorkspaceFolderListResult) => void;
  close: () => void;
}

// 最大并发上传数
const MAX_CONCURRENT_FILE_IMPORTS = 2;
// 单次上传文件最大体积 MB
const MAX_FILE_SIZE = 500;
// 单个文件最大体积 MB
const MAX_FILE_SIZE_SINGLE = 100;
// 单次上传文件最大数量
const MAX_FILE_COUNT = 100;

const ImportFileModal = forwardRef<ImportFileModalRef, ImportFileModalProps>((props, ref) => {
  const [visible, setVisible] = useState(false);
  const [fileList, setFileList] = useState<any[]>([]);
  const [uploading, setUploading] = useState(false);
  const [record, setRecord] = useState<GetWorkspaceFolderListResult | null>(null);
  const [form] = Form.useForm();

  const uploadRef = useRef<ShowUploadFileRefHandle>();

  useImperativeHandle(ref, () => ({
    open: (record: GetWorkspaceFolderListResult) => {
      setVisible(true);
      setFileList([]);
      setRecord(record);
      form.setFieldsValue({path: record.path});
    },
    close: () => handleClose()
  }));

  useDrop(document.querySelector('.workarea-upload-box .acud-upload-list'), {
    onDrop: (e) => {
      e.preventDefault();
      e.stopPropagation();
      if (e.dataTransfer) {
        const newEvent = new DragEvent('drop', {
          bubbles: true,
          cancelable: true,
          clientX: e.clientX,
          clientY: e.clientY,
          screenX: e.screenX,
          screenY: e.screenY,
          dataTransfer: e.dataTransfer
        });
        document.querySelector('.workarea-upload-box .acud-upload-drag-container')?.dispatchEvent(newEvent);
      }
    }
  });

  const [uploadRequestInfo, setUploadRequestInfo] = useState<any>([]);

  const handleUpload = async () => {
    if (fileList.length === 0) {
      return;
    }
    const queue = new TaskQueue(MAX_CONCURRENT_FILE_IMPORTS);
    setUploading(true);
    try {
      const newUploadRequestInfo = fileList.map((file) => {
        return {
          file: file,
          status: 'loading'
        };
      });
      setUploadRequestInfo(newUploadRequestInfo);
      setVisible(false);
      uploadRef.current?.expand();
      await Promise.allSettled(fileList.map((file) => queue.add(() => uploadRequest(file))));
      props.onSuccess?.();
    } catch (error) {
      console.error('文件上传失败:', error);
    } finally {
      setUploading(false);
    }
  };

  function updateUploadRequestInfo(file: any, obj: any) {
    setUploadRequestInfo((pre) => {
      return pre.map((item) => {
        if (file.uid === item.file.uid) {
          return {...item, ...obj};
        }
        return item;
      });
    });
  }

  // 单文件上传逻辑
  async function uploadRequest(file: any) {
    const formData = new FormData();
    formData.append('file', file);
    updateUploadRequestInfo(file, {status: 'loading'});
    try {
      const res = await uploadFile({
        workspaceId: props.workspaceId!,
        parentId: record!.id,
        formData,
        onProgress: (event) => {
          updateUploadRequestInfo(file, {percent: Math.round((event.loaded / event.total) * 100)});
        }
      });
      if (res?.success != true) {
        throw new Error('文件上传失败');
      }
      updateUploadRequestInfo(file, {status: 'success'});
    } catch (error) {
      console.error('文件上传失败:', error);
      updateUploadRequestInfo(file, {status: 'failed'});
    }
  }

  function handleClose() {
    setVisible(false);
  }

  const uploadProps: UploadProps = {
    multiple: true,
    accept: '*/*',
    onRemove: (file) => {
      const index = fileList.indexOf(file);
      const newFileList = fileList.slice();
      newFileList.splice(index, 1);
      setFileList(newFileList);
    },
    beforeUpload: (file) => {
      // if (!validatePath(file.name)) {
      //   toast.error({
      //     message: FILE_NAME_ERROR_MESSAGE,
      //     duration: 3
      //   });
      //   return false;
      // }
      if (file.size > MAX_FILE_SIZE_SINGLE * 1024 * 1024) {
        toast.error({
          message: `单个文件大小不能超过${MAX_FILE_SIZE_SINGLE}MB`,
          duration: 3
        });
        return false;
      }
      const totalSize = fileList.reduce((sum, item) => sum + item.size, 0) + file.size;
      const isLt500M = totalSize <= 1024 * 1024 * MAX_FILE_SIZE;
      if (!isLt500M) {
        toast.error({
          message: `单次上传文件总大小不能超过${MAX_FILE_SIZE}MB`,
          duration: 3
        });
        return false;
      }
      if (fileList.length === MAX_FILE_COUNT) {
        toast.error({
          message: `最多上传${MAX_FILE_COUNT}个文件`,
          duration: 3
        });
        return false;
      }
      // 简单的排重
      const uniqueKey = (fileItem: File) => `${fileItem.name}-${fileItem.size}`;
      setFileList((prev) => {
        if (_.some(prev, (item) => uniqueKey(item) === uniqueKey(file))) {
          return prev;
        }
        return [...prev, file];
      });
      return false; // 阻止自动上传
    },
    fileList
  };

  return (
    <>
      <Modal
        title="导入文件"
        visible={visible}
        onOk={handleUpload}
        onCancel={() => handleClose()}
        okText="上传"
        cancelText="取消"
        confirmLoading={uploading}
        width={800}
      >
        <div className={cx('workarea-upload', 'workarea-upload-box')}>
          <Upload.Dragger {...uploadProps}>
            <div className={cx('upload-dragger')}>
              {fileList.length === 0 ? (
                <div className="h-full flex items-center justify-center flex-col">
                  <p className="text-gray-600 mb-2">点击或者拖拽文件到这里上传</p>
                  <p className="text-gray-400 text-sm">
                    单次最多可上传100个文件，单个文件不超过100M，文件总大小不超过500M
                  </p>
                </div>
              ) : (
                <div className={cx('upload-control')}>
                  将文件拖拽到文件列表区域可继续添加，也可<span className={cx('upload-btn')}>点击此处</span>
                  选择文件
                </div>
              )}
            </div>
          </Upload.Dragger>
        </div>
        <Form form={form} layout="vertical" inputMaxWidth="100%">
          <Form.Item name="path" label="上传目录" rules={[{required: true}]}>
            <Input disabled />
          </Form.Item>
        </Form>
      </Modal>
      <ShowUploadFile
        ref={uploadRef}
        uploadList={uploadRequestInfo}
        setUploadListFun={setUploadRequestInfo}
        reUploadRequest={(file) => uploadRequest(file.originFileObj)}
      />
    </>
  );
});

ImportFileModal.displayName = 'ImportFileModal';

export default ImportFileModal;
