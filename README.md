# 百度智能云 React 工程项目

## 技术栈介绍

CBA（create-bce-app）是一款为百度智能云控制台前端项目提供的脚手架工具
本项目目前是基于 create-bce-app 提供的 **[@baidu/cba-cli](https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/tQvrZcAEPS/4yidKCir54/Yl586IZSCNxzLl)** 搭建的一个前端工程项目，<i> **整体框架&构建工程体系有一些调整的地方。** </i>

本项目目前 **【业务研发技术体系概览】** 如下：

- `React 18.3.1` 版本全家桶（react-dom@18.3.1、@reduxjs/toolkit@2.5.1、react-router-dom@6.21.1、等等）
- `acud` 基础UI组件库
  - [组件库文档](https://acud.now.baidu-int.com/components/docs)
  - [组件库icode](https://console.cloud.baidu-int.com/devops/icode/repos/baidu/bce-console/react-ui/tree/master)
- `@baidu/bce-react-toolkit` 业务组件库
  - [组件库文档](https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/tQvrZcAEPS/4yidKCir54/0dytktunzQWhlg)
  - [组件库icode](https://console.cloud.baidu-int.com/devops/icode/repos/baidu/baiducloud/bce-react-toolkit/tree/main)
- `@baidu/xicon-react-bigdata` 图标组件库
  - 整体跟 @baidu/xicon-react 使用一致，但是会有优化处理
  - [平台地址](https://yunshe.baidu-int.com/icon#/icon)
  - [组件库icode](https://console.cloud.baidu-int.com/devops/icode/repos/baidu/baiducloud/xicon/tree/bigdata)
- `ahooks@3.7.8` 基础通用hooks
  - [文档地址](https://ahooks.js.org/zh-CN/)

## 项目运行前置

1、安装依赖

```shell
npm install
# 如果~如果有遇到 依赖问题导致安装失败，尽量解决依赖版本不匹配的问题，最后才应该使用兜底方案 npm install --legacy-peer-deps
```

## 运行命令

```shell
# 本地联调沙盒环境
npm run dev
# or
npm run mock # 建议 mock 方式本地开发，跟 SDP 技术栈 mock 方式一致逻辑

# 生产环境构建
npm run build

# 停用，国际化语料提取 - 待后续改为 fe-i18n 国际化技术体系
npm run i18n:extract

# 停用，国际化语料上传 - 待后续改为 fe-i18n 国际化技术体系
npm run i18n:upload
```

> 🔔 若本地运行项目之后，浏览器出现 https 链接安全提示时，如下图，请直接键入 `thisisunsafe` ， 浏览器会信任 https 链接并自动刷新。
>
> ![](http://bj.bcebos.com/ibox-thumbnail98/********************************?authorization=bce-auth-v1%2Ffbe74140929444858491fbf2b6bc0935%2F2024-01-22T08%3A23%3A20Z%2F1800%2F%2F9725f457430ec343745b048dd1d9de3b4d826c12bd44cf614c38f94c8ff3f33b)

## 项目研发注意点

- `@baidu/cba-cli`、 `@baidu/cba-preset-console-react`、 `@baidu/bce-react-toolkit`、`@baidu/bos-react-sdk` 包要锁定具体版本，避免不规范发包或者发包不规范导致影响项目。
- js 不使用正在草案阶段的新API，tsconfig 中 lib 支持 ES2022 语法；
- less 文件可书写 [postcss-preset-env](https://preset-env.cssdb.org/features/) 提供的 CSS 语法；
- webpack 新增了很多alias别名，包括：@pages、@components、@api等，使其全部忽略 src 文件夹层级
- lodash 包非 ESM 导出：
  - 那么就不使用这种方式了：import isString from 'lodash/isString';
  - 全局解构方式导入 import {isString} from 'lodash'; 后续统一升级也方便；
- moment 包业务中先用吧，后面等在统一切换dayjs，推动acud、业务组件库也都一起切换；
- 国际化相关内容先不用理会，正常开发中文语言即可，后面我统一接入 fe-i18n 后再处理不迟

## 功能示例

### API Request

🔔 推荐的使用方式，如下：

在 `@/api` 路径下，新建业务对应的 `.ts` 文件，如 `auth.ts` 文件存放鉴权相关的接口：

```typescript
import {request} from '@baidu/bce-react-toolkit';

/** 激活产品角色 */
export function activateIamStsRole(params: {
  roleName: string;
  accountId: string;
  serviceId: string;
  policyId: string;
}): Promise<{
  status: number;
  success: boolean;
}> {
  return request({
    url: '/api/iam/sts/role/activate',
    method: 'POST',
    data: params
  });
}
```

> 另外新增 `silent` 参数，支持进行静默请求，当接口报错时，不进行 toast 提示, 具体使用示例如下：
>
> ```tsx
> return request({
>   url: '/api/iam/sts/role/activate',
>   method: 'POST',
>   data: params,
>   silent: true
> });
> ```

> 使用`raw` 参数，支持不进行数据提取，返回原始axios对象，适用下载等场景。
>
> ```tsx
> return request({
>   url: '/api/download',
>   method: 'POST',
>   data: params,
>   raw: true
> });
> ```

### 路由懒加载

在 `@/pages/index.tsx` 中进行菜单注册时，可通过 `React.lazy` 进行引用页面组件，示例代码如下：

```tsx
const DemoActivation = React.lazy(() => import(/* webpackChunkName: "Activation" */ '@pages/DemoActivation'));

/** 菜单定义 */
const menus: MenuItem[] = [
  {
    menuName: '概览',
    key: urls.activation,
    isNavMenu: true,
    Component: DemoActivation
  }
];
```

> 🔔 当需要自定义 chunk 文件的名称时，可通过 `webpackChunkName` 的注释来赋值 chunk 文件名称。

### 功能清单

[功能清单配置链接](http://bce.console.baidu-int.com/inventory/#/plat-ops-inventory/product/list)

flags文件为运行时文件，在每次项目启动或打包时都会根据配置文件中的templateId和flags重新生成。
使用方式，示例如下：

```tsx
import flags from '@/flags';

export default Demo = (props) => {
  return (
    <div>
      {flags.SmsDwzDisable && <p>使用优惠券</p>}
      {flags.SmsDwzDisable && <p>使用短链</p>}
    </div>
  );
};
```

### 灰度验证

加入当前产品的模块名称为 `databuilder`， 则访问产品页面之后，通过设置 sessionStorage 进行灰度验证，参考文档:

- [产品前端灰度（手动解决方案）](https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/qMApQ04r-e/c69Mh3zXaa/X3CVlYwhnD_xuW)
- [百度智能云-研发辅助chrome扩展工具方案](https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/qMApQ04r-e/c69Mh3zXaa/MqwnA8_sCbTJDF)

手动代码灰度示例代码如下：

```javascript
sessionStorage.setItem('_databuilder_version_', '1.x.x.1');
```

### Rdeux 应用示例

- src/store/index.ts
- src/store/sumSlice.ts
- src/pages/DemoRedux/index.tsx

### 自定义图标

使用方式

1. UI给的 SVG 图标，放值在 `src/assets/svg` 文件夹下，命名规则使用中划线分割，如：workflow-save.svg
2. 执行 `npm run svg` 命令，自动压缩，去除宽高，使用父级大小颜色
3. 使用 先引入 组件 `import IconSvg from '@components/IconSvg';`
4. 在需要展示的地方使用

```javascript
 // 应用 src/assets/svg/workflow-save.svg 这个文件
 <IconSvg type="workflow-save" />
 //颜色大小可单独配置
 <IconSvg type="workflow-save" size="20" color="#fff" />
```
