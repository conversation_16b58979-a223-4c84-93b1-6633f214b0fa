/**
 * 公有云 - 开通页
 */
import React, {useCallback, useState} from 'react';
import {AppContextActionType, useAppContext, useFrameworkContext} from '@baidu/bce-react-toolkit';
import {Button, Checkbox, Modal, Tooltip, toast} from 'acud';
import {useBoolean} from 'ahooks';
import {useNavigate} from 'react-router-dom';

import {activateIamStsRole, checkFullControl, getGlobalPermission} from '@api/auth';

import urls from '@utils/urls';

import styles from './banner.module.less';
import store from '@store/index';

const Banner: React.FC = () => {
  const [agreeTipVisible, setAgreeTipVisible] = useState(false);
  const [protocolChecked, setProtocolChecked] = useState(false);

  const {appState, appDispatch} = useAppContext();
  const {userId} = useFrameworkContext();
  const navigate = useNavigate();
  const [isActivateBtnLocked, {setTrue: lockActivateBtn, setFalse: unlockActivateBtn}] = useBoolean(false);

  const onProtocolChange = useCallback(
    (e: any) => {
      setProtocolChecked(e.target.checked);

      if (e.target.checked && agreeTipVisible) {
        setAgreeTipVisible(false);
      }
    },
    [agreeTipVisible]
  );

  const onClickActivateBtn = useCallback(async () => {
    if (!protocolChecked) {
      setAgreeTipVisible(true);
      return;
    }

    if (isActivateBtnLocked) {
      return;
    }

    lockActivateBtn();

    try {
      // 1.检查是否具备全控权限
      const checkFullControlResult = await checkFullControl();
      if (!checkFullControlResult.success || !checkFullControlResult.result) {
        throw new Error('未开通fullcontrol权限，请联系产研');
      }

      // 2.激活产品
      const result = await activateIamStsRole({
        accountId: userId!
      });
      if (!result.success) {
        toast.error({
          message: '激活产品失败，请重试',
          duration: 3
        });
        throw new Error('激活产品失败，请重试');
      }

      // 3.获取全局权限
      const globalPermission = await getGlobalPermission();
      if (!globalPermission.success) {
        toast.error({
          message: '全局权限获取失败，请重试',
          duration: 3
        });
        throw new Error('全局权限获取失败，请重试');
      }

      // 4.更新状态
      appDispatch({
        type: AppContextActionType.ACTIVATE_PRODUCT
      });
      store.dispatch({
        type: 'globalAuth/updateGlobalAuth',
        payload: globalPermission.result
      });

      // 5.跳转到工作空间
      navigate(urls.manageWorkspace);
    } catch (error) {
      console.error(error);
    }

    unlockActivateBtn();
  }, [
    appDispatch,
    isActivateBtnLocked,
    lockActivateBtn,
    navigate,
    protocolChecked,
    unlockActivateBtn,
    userId
  ]);

  return (
    <div className={styles.container}>
      <div className={styles.contentContainer}>
        <div className={styles.title}>千帆数据智能平台 DataBuilder</div>
        <div className={styles.desc}>
          开通千帆数据智能平台 DataBuilder，将自动在IAM中为您创建服务角色并授权相关权限。
        </div>

        <div className={styles.operateContainer}>
          <div className={styles.protocolContainer}>
            <Tooltip
              title={<span className={styles.agreeTip}>请先阅读并同意服务协议信息</span>}
              visible={agreeTipVisible}
            >
              <Checkbox onChange={onProtocolChange} value={protocolChecked}></Checkbox>
            </Tooltip>
            <span className={styles.agreeText}>同意使用</span>
            <a href="https://console.bce.baidu.com/iam/agreement-v2.html" target="_blank" rel="noreferrer">
              《百度智能云线上订购协议》
            </a>
          </div>
          <Button type="primary" className={styles.activateBtn} onClick={onClickActivateBtn}>
            立即开通
          </Button>
        </div>

        <div className={styles.banner}></div>
      </div>
    </div>
  );
};

export default Banner;
