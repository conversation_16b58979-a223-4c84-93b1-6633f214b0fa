/**
 * @file catalog - 工作空间tab - 分配空间弹窗
 * <AUTHOR>
 */

import {FC, useCallback, useEffect, useState} from 'react';
import {Form, Select, Modal, toast} from 'acud';
import {queryWorkspaceList, IWorkspace} from '@api/workspace';
import {
  IQueryCatalogWorkspaceListParams,
  assignCatalogWorkspace,
  getCatalogWorkspaceList as getAllCatalogWorkspaceList
} from '@api/metaRequest';
import {EWorkspaceRole, EWorkspaceStatus} from '@pages/Workspace/constants';
import {Ellipsis} from '@baidu/bce-react-toolkit';
import styles from './CreateModal.module.less';

interface IWorkspaceWithDisabled extends IWorkspace {
  disabled?: boolean;
}
interface ICreateModalProps {
  /** catalog id */
  catalogId?: string;
  /** 弹窗是否打开 */
  isModalVisible: boolean;
  /** 关闭弹窗 */
  handleCloseModal: () => void;
  /** 刷新catalog工作空间列表 */
  getCatalogWorkspaceList: (params: IQueryCatalogWorkspaceListParams) => void;
}

const CreateModal: FC<ICreateModalProps> = ({
  catalogId,
  isModalVisible,
  handleCloseModal,
  getCatalogWorkspaceList
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [sourceLoading, setSourceLoading] = useState(false);
  const [workspaceList, setWorkspaceList] = useState<IWorkspaceWithDisabled[]>([]);

  // 打开弹窗，初始化全量工作空间列表(仅有管理员权限的工作空间)，筛选出已分配的工作空间进行禁用处理
  useEffect(() => {
    if (isModalVisible) {
      setSourceLoading(true);
      Promise.all([
        // 获取有权限的全量工作空间列表
        queryWorkspaceList({
          pageNo: 1,
          pageSize: 10000
        }),
        // 获取当前catalog下全量工作空间列表
        getAllCatalogWorkspaceList(catalogId, {
          pageNo: 1,
          pageSize: 10000
        })
      ])
        .then(([workspaceRes, catalogWorkspaceRes]) => {
          let adminWorkspaceList = []; // 管理员权限的工作空间列表，且状态不为ERROR
          let allCatalogWorkspaceIds = []; // 当前catalog下全量工作空间id列表
          if (workspaceRes.success) {
            adminWorkspaceList =
              workspaceRes.result?.items?.filter(
                (item) => item.role === EWorkspaceRole.ADMIN && item.status !== EWorkspaceStatus.ERROR
              ) || [];
          }
          if (catalogWorkspaceRes.success) {
            allCatalogWorkspaceIds = catalogWorkspaceRes?.result?.items.map((item) => item.workspaceId);
          }
          adminWorkspaceList = adminWorkspaceList.map((item) => {
            return {
              ...item,
              disabled: allCatalogWorkspaceIds.includes(item.id) // 当前catalog下已分配的工作空间禁用
            };
          });
          setWorkspaceList(adminWorkspaceList);
        })
        .finally(() => {
          setSourceLoading(false);
        });
    } else {
      setWorkspaceList([]);
    }
  }, [isModalVisible]);

  // 关闭弹窗
  const onCloseModal = useCallback(() => {
    handleCloseModal();
    form.resetFields();
  }, [form, handleCloseModal]);

  // 提交表单
  const handleConfirm = useCallback(() => {
    // 表单校验
    form
      .validateFields()
      .then((values) => {
        const {ids} = values;
        const params = ids.map((item) => {
          const {id, name} = workspaceList.find((w) => w.id === item)!;
          return {
            workspaceId: id,
            workspaceName: name
          };
        });
        setLoading(true);

        // 分配空间接口调用
        assignCatalogWorkspace(catalogId, {items: params})
          .then((res) => {
            if (res?.success) {
              toast.success({
                message: '分配成功',
                duration: 5
              });

              onCloseModal();
              getCatalogWorkspaceList({
                pageNo: 1
              });
            }
          })
          .finally(() => {
            setLoading(false);
          });
      })
      .catch(() => {});
  }, [form, workspaceList, assignCatalogWorkspace, onCloseModal, getCatalogWorkspaceList]);

  return (
    <Modal
      closable={true}
      title="分配空间"
      width={500}
      bodyStyle={{minHeight: 0}}
      visible={isModalVisible}
      onOk={handleConfirm}
      onCancel={onCloseModal}
      okButtonProps={{loading}}
      destroyOnClose={true}
      className={styles['assign-workspace-modal']}
    >
      <Form labelAlign="left" colon={false} labelWidth={80} form={form}>
        <Form.Item label="选择工作空间" name="ids" rules={[{required: true, message: '请选择工作空间'}]}>
          <Select
            loading={sourceLoading}
            placeholder="请选择工作空间"
            allowClear
            filterOption={(inputValue, option) =>
              option?.title.toString().toLowerCase().indexOf(inputValue.toLowerCase()) >= 0
            }
            showSelectAll={workspaceList.findIndex((item) => !item.disabled) !== -1}
            mode="multiple"
            style={{width: '100%'}}
            dropdownClassName={styles['assign-workspace-modal-select-dropdown']}
          >
            {workspaceList.map((item) => (
              <Select.Option value={item.id} key={item.id} title={item.name} disabled={item.disabled}>
                <Ellipsis tooltip={item.name}>{item.name}</Ellipsis>
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default CreateModal;
