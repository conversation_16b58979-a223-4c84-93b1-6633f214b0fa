.catalog-workspace-tab-wrapper {
  width: 100%;
  height: 100%;
  position: relative;

  .space-tip {
    padding: 6px 12px;
    background-color: #e6f0ff;
    border-radius: 6px;
    margin-bottom: 16px;
  }

  :global {
    .acud-table-cell-ellipsis {
      .toolkit-ellipsis {
        width: 90%;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
      }
    }

    .acud-empty {
      min-height: 50vh;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-flow: column;
    }
  }

  .admin-empty-container {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    .admin-empty-block {
      display: flex;
      flex-flow: column;
      align-items: center;
      justify-content: center;
      background-color: #f7f6f6;
      width: 500px;
      height: 250px;
      border-radius: 8px;
      margin-top: -100px;

      .admin-empty-block-avatar {
        width: 50px;
        height: 50px;
        background-color: lightgrey;
      }
    }
  }

  .operation-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;

    .left-btn-container {
      display: flex;
      align-items: center;

      button {
        margin-left: 16px;
      }
    }

    .right-container {
      display: flex;
      align-items: center;
      gap: 10px;

      .search-container {
        width: 240px;
      }
    }
  }

  .pagination-container {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;

    :global {
      .acud-pagination {
        width: fit-content;
      }
    }
  }
}
