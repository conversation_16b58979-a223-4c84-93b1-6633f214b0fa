/**
 * 实现自定义标签 支持 选中
 */

import React from 'react';
import styles from './index.module.less';
/**
 * 自定义 图标配置 使用方式 <IconSvg type="workflow-import" />
 * type 为 图片文件名称 @assets/svg/${type}.svg
 * size 为 图片大小 默认 1em，可以传递 16px 等单位
 * color 为 图片颜色
 */
interface TagStatusProps {
  name: string;
  color?: string;
  number?: number;
  checked?: boolean;
  onClick?: () => void;
}
const TagStatus: React.FC<TagStatusProps> = ({name, color, number, checked = false, ...props}) => {
  return (
    <div className={`${styles['tag-status']} ${checked ? styles['checked'] : ''}`} {...props}>
      {color && <div className={styles['circle']} style={{backgroundColor: color}}></div>}
      <div className={styles['name']}>{name}</div>
      <div className={styles['number']}>{number}</div>
    </div>
  );
};

export default TagStatus;
