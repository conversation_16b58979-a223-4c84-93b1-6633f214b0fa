import {Col, Row, Space, Tag, Tooltip} from 'acud';
import React, {useMemo, useState} from 'react';
import styles from './index.module.less';
import {Table} from 'acud';
import {IJobInstanceTaskNode} from '@api/jobInstance';
import {JobInstanceTaskStatus, JobInstanceTaskStatusMap} from '@pages/JobInstance/constants';
import {useMemoizedFn, useMount} from 'ahooks';
import {IJsonOperatorData} from '@pages/JobWorkflow/Jobs/components/X6Page/type';
import {Ellipsis} from '@baidu/bce-react-toolkit';
import TagStatus from '@pages/JobInstance/ResultPage/components/TaskListTable/TagStatus';
const {CheckableTag}: any = Tag;

// 本地过滤
const TaskStatusArr = [
  {
    name: JobInstanceTaskStatusMap[JobInstanceTaskStatus.RUNNING_EXECUTION].label,
    value: JobInstanceTaskStatus.RUNNING_EXECUTION,
    color: '#144BCC'
  },
  {
    name: JobInstanceTaskStatusMap[JobInstanceTaskStatus.SUCCESS].label,
    value: JobInstanceTaskStatus.SUCCESS,
    color: '#17B342'
  },
  {
    name: JobInstanceTaskStatusMap[JobInstanceTaskStatus.FAILURE].label,
    value: JobInstanceTaskStatus.FAILURE,
    color: '#E23A10'
  },
  {
    name: JobInstanceTaskStatusMap[JobInstanceTaskStatus.PENDING].label,
    value: JobInstanceTaskStatus.PENDING,
    color: '#84868C'
  }
];

/**
 * @param  list 列表数据
 * @param  taskId 选择的taskId
 * @param  setTaskId 选择的修改taskId
 */

const JobInstanceTaskListTable: React.FC<{
  list?: IJobInstanceTaskNode[];
  taskId: string;
  setTaskId: (taskId: string) => void;
}> = ({list = [], taskId, setTaskId}) => {
  const [checkedStatus, setCheckedStatus] = useState<string[]>([]);

  const columns: any = [
    {
      title: '任务名称',
      dataIndex: 'name',
      fixed: 'left',
      width: 180,
      render: (name: string) => {
        return <Ellipsis tooltip={name}>{name}</Ellipsis>;
      }
    },
    {
      title: '任务类别',
      dataIndex: 'type',
      width: 130
    },
    {
      title: '包含节点',
      dataIndex: 'operatorList',

      width: 80,
      render: (operatorList: IJsonOperatorData[]) => (
        <Tooltip title={operatorList?.map((item) => item.name).join(',')}>
          {operatorList?.length || 0}
        </Tooltip>
      )
    },
    {
      title: '状态',
      dataIndex: 'taskStatus',
      width: 80,
      render: (taskStatus: string) =>
        JobInstanceTaskStatusMap[taskStatus]?.label ? (
          <Tag color={JobInstanceTaskStatusMap[taskStatus]?.color}>
            {JobInstanceTaskStatusMap[taskStatus]?.label}
          </Tag>
        ) : (
          '-'
        )
    },
    {
      title: '开始时间',
      width: 154,
      dataIndex: 'startTime',
      render: (startTime: string) => (startTime ? startTime : '-')
    },
    {
      title: '结束时间',
      width: 154,
      dataIndex: 'endTime',
      render: (endTime: string) => (endTime ? endTime : '-')
    }
  ];

  // 选择任务
  const rowSelection = {
    onChange: (selectedRowKeys: React.Key[]) => {
      setTaskId(selectedRowKeys.join(','));
    },

    selectedRowKeys: [taskId]
  };

  // 切换筛选
  const handleChange = useMemoizedFn((tagValue) => {
    // 如果点击全部
    if (tagValue === JobInstanceTaskStatus.ALL) {
      setCheckedStatus([]);
      return;
    }
    // 如果点击其他
    const flag = checkedStatus.includes(tagValue);

    let nextSelectedTags = checkedStatus.filter((t) => t !== JobInstanceTaskStatus.ALL);
    if (flag) {
      nextSelectedTags = checkedStatus.filter((t) => t !== tagValue);
    } else {
      nextSelectedTags = [...nextSelectedTags, tagValue];
    }
    setCheckedStatus(nextSelectedTags);
  });

  const statusNum = useMemo(() => {
    const arrNum = {
      [JobInstanceTaskStatus.RUNNING_EXECUTION]: 0,
      [JobInstanceTaskStatus.SUCCESS]: 0,
      [JobInstanceTaskStatus.FAILURE]: 0,
      [JobInstanceTaskStatus.PENDING]: 0,
      [JobInstanceTaskStatus.ALL]: 0
    };
    list.forEach((item) => {
      arrNum[item.taskStatus]++;
      arrNum[JobInstanceTaskStatus.ALL]++;
    });
    return arrNum;
  }, [list]);
  return (
    <div className={styles['table']}>
      <Row className={styles['table-header']}>
        <Col span={24}>
          <Space>
            <TagStatus
              checked={checkedStatus.length === 0}
              onClick={() => handleChange(JobInstanceTaskStatus.ALL)}
              name="全部"
              number={statusNum[JobInstanceTaskStatus.ALL]}
            ></TagStatus>
            {TaskStatusArr.map((tag) => (
              <TagStatus
                checked={checkedStatus.indexOf(tag.value) > -1}
                onClick={() => handleChange(tag.value)}
                name={tag.name}
                number={statusNum[tag.value]}
                color={tag.color}
                key={tag.value}
              ></TagStatus>
            ))}
          </Space>
        </Col>
      </Row>
      <Table
        scroll={{x: '100%'}}
        rowKey="id"
        rowSelection={{
          type: 'radio',
          ...rowSelection
        }}
        columns={columns}
        dataSource={list.filter(
          (item) => checkedStatus.length === 0 || checkedStatus.indexOf(item.taskStatus) > -1
        )}
      />
    </div>
  );
};

export default JobInstanceTaskListTable;
