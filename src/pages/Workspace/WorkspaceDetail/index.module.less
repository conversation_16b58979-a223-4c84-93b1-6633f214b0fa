.workspace-detail-container {
  width: 100%;
  background-color: #fff;
  padding: 16px 24px;
  display: flex;
  flex-direction: column;
  overflow-y: auto;

  .workspace-detail-header {
    margin: 16px 0 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .workspace-detail-delete {
      color: #151b26;
      &:hover {
        background: rgba(7, 12, 20, 0.06);
        border: 1px solid #e8e9eb;
      }
    }

    .workspace-detail-title {
      max-width: 800px;
      font-family: PingFangSC-Medium;
      font-size: 22px;
      line-height: 32px;
      flex: 1;
      min-width: 0;
      margin-right: 20px;
    }
  }

  .workspace-detail-container-breadcrumb-name {
    max-width: 500px;
    display: inline-flex;
    align-items: center;
    font-family: PingFangSC-Medium;
  }

  .workspace-detail-tabs {
    flex: 1;
    min-height: 600px;

    :global {
      .acud-tabs-nav {
        margin-bottom: 20px;
      }
      .acud-tabs-content {
        height: 100%;
      }
    }
  }

  :global {
    .acud-breadcrumb {
      display: flex;
      flex-flow: row nowrap;

      a {
        font-family: PingFangSC-Regular;
        font-size: 12px;
        color: #84868c;
      }

      & > span {
        display: inline-flex;
        align-items: center;
      }

      .acud-breadcrumb-separator,
      .acud-breadcrumb-link {
        display: inline-block;
      }

      .acud-breadcrumb-separator {
        margin: 0 8px;
        display: inline-flex;
        align-items: center;
      }
    }
  }
}
