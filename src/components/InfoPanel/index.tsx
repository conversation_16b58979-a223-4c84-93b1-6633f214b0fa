/**
 * InfoPanel 组件
 *
 * 该组件用于展示信息面板，以 key:value 的形式显示一组信息。
 * 每行信息由一个标签和一个对应的值组成，使用 `Row` 和 `Col` 布局组件进行排列。
 * <AUTHOR>
 */

import React from 'react';
import {Col, Row} from 'acud';

import './index.less';

interface IInfoPanelProps {
  infoList: {label: string; value: string | React.ReactNode}[];
  title?: string | React.ReactNode;
}

const klass = 'info-panel-show-com';

const InfoPanel = (props: IInfoPanelProps) => {
  const {infoList = [], title} = props;
  return (
    <>
      {title ? React.isValidElement(title) ? title : <h4 className={`${klass}-title`}>{title}</h4> : null}
      <div className={`${klass}`}>
        {infoList.map((item) => (
          <Row key={item.label}>
            <Col span={6} className={`${klass}-label`}>
              {item.label}
            </Col>
            <Col span={18} className={`${klass}-value`}>
              {item.value || '-'}
            </Col>
          </Row>
        ))}
      </div>
    </>
  );
};

export default React.memo(InfoPanel);
