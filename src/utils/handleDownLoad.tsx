import {AxiosResponse} from 'axios';
import {toast} from 'acud';
import {GlobalNotifyDescription, GlobalNotifyMessage} from '@baidu/bce-react-toolkit';
/**
 * 处理下载
 * @param 接口返回的原数据
 *
 */
export const handleDownLoad = async (res: AxiosResponse) => {
  if (res.data instanceof Blob && res.data?.type === 'application/json') {
    const {headers, config} = res;
    const text = await res.data.text();
    const data = JSON.parse(text);
    const requestId = headers?.['x-bce-request-id'];
    const ticket = window?.$framework?.frameworkStore?.constants?.domains?.ticket || '';
    const module = config?.url?.split('/')[2].toUpperCase() || '';
    toast.error({
      message: (
        <GlobalNotifyMessage
          requestId={requestId!}
          ticket={ticket}
          module={module}
          message={data.message.global}
        ></GlobalNotifyMessage>
      ),
      description: <GlobalNotifyDescription requestId={requestId}></GlobalNotifyDescription>,
      key: headers?.['x-bce-request-id'],
      className: 'global-toast-error-container'
    });
    return;
  }
  const blob = new Blob([res.data], {type: 'application/octet-stream'});
  const downloadElement = document.createElement('a');

  // 创建下载链接
  const href = window.URL.createObjectURL(blob);
  let fileName = res?.headers?.['content-disposition']
    ? res?.headers?.['content-disposition']?.split('attachment; filename')?.[1]
    : new Date().getTime();

  // 处理 UTF-8 编码的文件名
  if (fileName?.startsWith("*=UTF-8''")) {
    fileName = decodeURIComponent(fileName.substring(9));
  } else if (fileName?.startsWith('=')) {
    fileName = fileName.substring(1);
  }

  downloadElement.href = href;
  downloadElement.download = fileName;
  document.body.appendChild(downloadElement);
  downloadElement.click();
  document.body.removeChild(downloadElement);
  window.URL.revokeObjectURL(href);
};
