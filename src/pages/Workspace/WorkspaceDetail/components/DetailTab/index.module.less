.workspace-detail-tab {
  border-radius: 3px;
  border: 1px solid #e8e8e8;
  overflow: hidden;

  &-item {
    width: 100%;
    display: flex;
    border-bottom: 1px solid #e8e8e8;

    &-copy {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      margin-left: 8px;
      cursor: pointer;
      width: 20px;
      height: 20px;

      button {
        padding: 0;
        border: none;
      }

      &:hover {
        background-color: #f0f1f1;
        border-radius: 4px;
      }

      & > div {
        width: 20px;
        padding-left: 2px;
      }

      svg {
        vertical-align: text-top;
      }
    }

    &-desc-edit {
      margin-left: 8px;
      padding: 0 !important;
      width: 20px;
      height: 20px !important;
      border-radius: 4px !important;

      &:hover {
        background-color: #f0f1f1 !important;
      }
    }

    &-label,
    &-content {
      padding: 8px 23px;
      line-height: 20px;
    }

    &-label {
      font-family: PingFangSC-Medium;
      width: 280px;
      display: flex;
      align-items: center;
      background-color: #f7f7f9;
    }

    &-content {
      font-family: PingFangSC-Regular;
      flex: 1;
      min-width: 0;
      padding-left: 38px;
      border-left: 1px solid #e8e8e8;
      display: flex;
      align-items: baseline;
      word-break: break-all;

      &-popconfirm {
        :global {
          .acud-popover-message {
            width: 344px;
            padding-top: 2px;

            .acud-popover-message-title {
              padding: 0;
            }
          }

          .acud-popover-buttons {
            button {
              width: 72px;
              height: 32px;
              border-radius: 4px;
            }
          }
        }
      }
    }
  }

  &-form-name {
    :global .acud-form-item{
        margin-bottom: 0 !important;
    }
  }

  &-item:last-child {
    border-bottom: none;
  }
}
