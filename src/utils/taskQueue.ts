type Task<T> = () => Promise<T>;

interface QueueItem<T> {
  task: Task<T>;
  resolve: (value: T) => void;
  reject: (reason?: any) => void;
}

export class TaskQueue {
  private queue: QueueItem<any>[] = [];
  private runningCount: number = 0;
  private maxConcurrent: number;

  constructor(maxConcurrent: number = 2) {
    this.maxConcurrent = maxConcurrent;
  }

  // 设置最大并发数
  setMaxConcurrent(max: number) {
    this.maxConcurrent = max;
    this.processQueue();
  }

  // 添加任务到队列
  add<T>(task: Task<T>): Promise<T> {
    return new Promise<T>((resolve, reject) => {
      this.queue.push({
        task,
        resolve,
        reject
      });
      this.processQueue();
    });
  }

  // 处理队列
  private async processQueue() {
    if (this.runningCount >= this.maxConcurrent || this.queue.length === 0) {
      return;
    }

    const item = this.queue.shift();
    if (!item) return;

    this.runningCount++;

    try {
      const result = await item.task();
      item.resolve(result);
    } catch (error) {
      item.reject(error);
    } finally {
      this.runningCount--;
      this.processQueue();
    }
  }

  // 获取当前队列长度
  get length(): number {
    return this.queue.length;
  }

  // 获取当前正在执行的任务数
  get running(): number {
    return this.runningCount;
  }

  // 清空队列
  clear() {
    this.queue = [];
  }
}
