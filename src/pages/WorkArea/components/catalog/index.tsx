/**
 * 树形目录组件
 * 该组件用来展示目录结构
 * 通过getWorkspaceFolderList来获取目录结构
 * 通过onSelect来设置当前的目录
 * <AUTHOR>
 */
import React, {useState, useEffect, useContext, forwardRef, useImperativeHandle, useRef} from 'react';
import type {DataNode} from 'acud/es/tree';
import {getWorkspaceFolderList} from '@api/WorkArea';
import type {GetWorkspaceFolderListResult} from '@api/WorkArea';
import useUrlState from '@ahooksjs/use-url-state';
import classNames from 'classnames/bind';
import styles from './index.module.less';
import {WorkspaceContext} from '@pages/index';
import LazyTree from './LazyTree';
import _ from 'lodash';
import {updateTreeData} from '../../utils';
import {FOLDER_NAME_MAP} from '@pages/WorkArea/config';
const cx = classNames.bind(styles);

interface CatalogTreeProps {
  onSelect?: (businessData?: GetWorkspaceFolderListResult) => void;
  onDefaultSelect?: (businessData?: GetWorkspaceFolderListResult) => void;
  currentDirObj?: GetWorkspaceFolderListResult;
  path?: GetWorkspaceFolderListResult[];
}

interface treeNode extends DataNode {
  businessData?: GetWorkspaceFolderListResult;
}

export interface CatalogTreeRef {
  refreshTreeNode: (nodeId: string) => Promise<void>;
}

export const loadTreedData = ({
  workspaceId,
  nodeId,
  titleMap
}: {
  workspaceId: string;
  nodeId?: React.Key;
  titleMap?: Record<string, string>; // 目录类型对应的名称
}): Promise<treeNode[]> => {
  return getWorkspaceFolderList({
    parentId: nodeId as string,
    workspaceId
  }).then((res) => {
    if (!res.success) {
      return [];
    }
    return _.map(res.result, (item: any) => {
      const name = titleMap?.[item.type] || item.name;
      return {
        title: name,
        key: item.id,
        // 添加业务相关数据
        businessData: {
          ...item,
          name
        },
        isLeaf: item.type === 'TRASH'
      };
    });
  });
};

const CatalogTree = forwardRef<CatalogTreeRef, CatalogTreeProps>(
  ({onSelect, onDefaultSelect, currentDirObj}, ref) => {
    const initKey = currentDirObj?.id;
    const [currentKey, setCurrentKey] = useState<React.Key>(initKey);
    const [homeTreeData, setHomeTreeData] = useState<treeNode[]>([]);
    const [otherTreeData, setOtherTreeData] = useState<treeNode[]>([]);
    const [urlState, setUrlState] = useUrlState();
    const {workspaceId} = useContext(WorkspaceContext);

    useImperativeHandle(ref, () => ({
      // 刷新节点
      refreshTreeNode: async (nodeId: string) => {
        const children = await loadTreedData({
          workspaceId,
          nodeId,
          titleMap: _.pick(FOLDER_NAME_MAP, ['TRASH', 'USERS'])
        });
        setHomeTreeData((origin) => updateTreeData<GetWorkspaceFolderListResult>(origin, nodeId, children));
        setOtherTreeData((origin) => updateTreeData<GetWorkspaceFolderListResult>(origin, nodeId, children));
      }
    }));

    // 初始化目录
    const loadingRef = useRef<boolean>(true);
    useEffect(() => {
      if (!urlState.workspaceId) {
        return;
      }
      loadingRef.current = true;
      loadTreedData({workspaceId: urlState.workspaceId, titleMap: FOLDER_NAME_MAP})
        .then((res) => {
          const homeTree = res.filter((item) => item.businessData.type === 'HOME');
          const otherTree = res.filter((item) => item.businessData.type !== 'HOME');
          setHomeTreeData(homeTree);
          setOtherTreeData(otherTree);
        })
        .finally(() => {
          loadingRef.current = false;
        });
    }, [urlState.workspaceId]);

    // 更新当前选中节点
    useEffect(() => {
      if (urlState.folderId) {
        setCurrentKey(urlState.folderId);
      }
    }, [urlState.folderId, setCurrentKey]);

    // 默认选中home节点
    useEffect(() => {
      if (!loadingRef.current && !urlState.folderId && homeTreeData.length > 0) {
        onDefaultSelect?.(homeTreeData[0].businessData);
        setCurrentKey(homeTreeData[0].key);
      }
    }, [urlState.folderId, homeTreeData, onDefaultSelect]);

    function handleSelect(businessData?: GetWorkspaceFolderListResult) {
      onSelect?.(businessData);
      setCurrentKey(businessData?.id);
    }

    function handleTreeDataChange(treeType: 'home' | 'other') {
      return (key: React.Key, children: treeNode[]) => {
        if (treeType === 'home') {
          setHomeTreeData((origin) => updateTreeData<GetWorkspaceFolderListResult>(origin, key, children));
        } else {
          setOtherTreeData((origin) => updateTreeData<GetWorkspaceFolderListResult>(origin, key, children));
        }
      };
    }

    return (
      <div className={cx('w-full h-full', 'catalog')}>
        <div className={cx('catalog-title')}>工作区</div>
        <div className={cx('tree-wrap')}>
          <LazyTree
            currentKey={currentKey}
            dataSource={homeTreeData}
            onTreeDataChange={handleTreeDataChange('home')}
            onSelect={handleSelect}
          />
          <LazyTree
            currentKey={currentKey}
            dataSource={otherTreeData}
            onTreeDataChange={handleTreeDataChange('other')}
            onSelect={handleSelect}
          />
        </div>
      </div>
    );
  }
);

CatalogTree.displayName = 'CatalogTree';

export default CatalogTree;
