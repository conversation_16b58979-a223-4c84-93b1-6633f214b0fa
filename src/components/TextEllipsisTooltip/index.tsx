import React, {ReactNode, useEffect, useLayoutEffect, useMemo, useRef, useState} from 'react';
import {Tooltip} from 'acud';
import {TooltipProps} from 'acud/lib/tooltip';
import ResizeObserver from 'resize-observer-polyfill';

import './index.less';

export interface ITextEllipsisProps {
  /** tooltip 文字 */
  tooltip?: ReactNode;
  /** tooltip属性 */
  tooltipProps?: Partial<TooltipProps>;
  /**
   * @description 文字占位宽度
   * @default "100%"
   * */
  width?: number | string;
  /**
   * @description 截取行数
   * @default 1
   * */
  clampNum?: number;
  children?: ReactNode;
  keyword?: string;
  keywordColor?: string;
}

const prefixCls = 'db-text-ellipsis';

/** 文字省略 tooltip 组件
 * @param {ReactNode} tooltip tooltip文字
 * @param {TooltipProps} tooltipProps tooltip属性
 * @param {number} width 文字占位宽度
 * @param {number} clampNum 截取行数
 * @param {ReactNode} children
 */
const TextEllipsis: React.FC<ITextEllipsisProps> = ({
  tooltip = '',
  width,
  children,
  tooltipProps,
  clampNum = 1,
  keyword = '',
  keywordColor = ''
}) => {
  const contentRef = useRef<HTMLDivElement>(null);
  const wrapperRef = useRef<HTMLDivElement>(null);
  const [showTooltip, setShowTooltip] = useState(false);
  const [tooltipWidth, setTooltipWidth] = useState(width ?? '100%');

  const tooltipTitle = useMemo(() => {
    return tooltipProps?.title ?? tooltip;
  }, [tooltip, tooltipProps]);

  useEffect(() => {
    if (!tooltipTitle) {
      return () => {};
    }
    const handleResize = () => {
      if (contentRef.current) {
        const isShowTooltip =
          clampNum > 1
            ? contentRef.current.scrollHeight > contentRef.current.clientHeight
            : contentRef.current.scrollWidth > contentRef.current.clientWidth;
        setShowTooltip(isShowTooltip);
      }
    };

    const resizeObserver = new ResizeObserver(handleResize);

    if (contentRef.current?.parentElement) {
      resizeObserver.observe(contentRef.current.parentElement);
    }

    return () => {
      resizeObserver.disconnect();
    };
  }, [children, tooltipTitle, clampNum]);

  useLayoutEffect(() => {
    // 如果自定了width，无需处理
    if (width) {
      return;
    }

    // 如果没有兄弟节点，无需处理
    const parent = wrapperRef.current?.parentElement;
    const parentChildren = parent?.childNodes;
    if (!parentChildren || parentChildren.length <= 1) {
      return;
    }

    const accordType = (element: any, typeArr: string[]) => {
      const display = getComputedStyle(element).getPropertyValue('display');
      return typeArr.includes(display);
    };

    // 父元素不是 block或者inline-block的节点，无需处理
    if (!accordType(parent, ['block', 'inline-block'])) {
      return;
    }

    const calcMarginValue = (element: any) => {
      const marginL = getComputedStyle(element).getPropertyValue('margin-left');
      const marginR = getComputedStyle(element).getPropertyValue('margin-right');
      return Number(marginL.replace('px', '')) + Number(marginR.replace('px', ''));
    };

    let widthValue = 0;

    // 获取wrapperRef.current的前后 【各个兄弟节点】参与计算
    let prevElement = wrapperRef.current?.previousElementSibling;
    let nextElement = wrapperRef.current?.nextElementSibling;

    while (prevElement && accordType(prevElement, ['inline', 'inline-block'])) {
      widthValue += (prevElement as HTMLElement).offsetWidth + calcMarginValue(prevElement);
      prevElement = prevElement.previousElementSibling;
    }

    while (nextElement && accordType(nextElement, ['inline', 'inline-block'])) {
      widthValue += (nextElement as HTMLElement).offsetWidth + calcMarginValue(nextElement);
      nextElement = nextElement.nextElementSibling;
    }

    widthValue += calcMarginValue(wrapperRef.current);
    setTooltipWidth('calc(100% - ' + widthValue + 'px)');
  }, []);

  const renderHightLight = ({text, keyword, keywordColor}) => {
    if (!keyword || !keyword.trim() || typeof text !== 'string') {
      return text;
    }
    return text.split(new RegExp(`(${keyword})`, 'gi')).map((c, i) =>
      c === keyword ? (
        <span key={i} style={{color: keywordColor}}>
          {c}
        </span>
      ) : (
        c
      )
    );
  };

  const content = useMemo(() => {
    return (
      <div ref={contentRef} className={`${prefixCls}-content clamp-${clampNum}`}>
        {renderHightLight({text: children, keyword, keywordColor})}
      </div>
    );
  }, [children, keyword, keywordColor]);

  return (
    <div className={prefixCls} ref={wrapperRef} style={{maxWidth: tooltipWidth}}>
      {tooltipTitle && showTooltip ? (
        <Tooltip title={tooltip} {...tooltipProps} overlayClassName={`${prefixCls}-tooltip`}>
          {content}
        </Tooltip>
      ) : (
        content
      )}
    </div>
  );
};

export default TextEllipsis;
