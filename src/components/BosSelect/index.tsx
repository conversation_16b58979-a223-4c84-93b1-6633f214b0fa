/**
 * @file bos选择组件封装
 * <AUTHOR>
 */

import {FC, useEffect, useState} from 'react';
import BosClient from '@api/bosClient';
// @ts-ignore
import {BucketSelect} from '@baidu/bos-react-sdk';
import '@baidu/bos-react-sdk/style.css';
import {bosRequest} from '@api/bosClient';
import './index.less';

interface IBosSelectProps {
  /** 值 */
  value: string;
  /** 选择回调 */
  handleSelect: (value: string) => void;
}

const BosSelect: FC<IBosSelectProps> = ({value, handleSelect}) => {
  const client = new BosClient({
    client: bosRequest
  });

  const [visible, setVisible] = useState(false); // 控制下拉弹窗显隐

  // bos-react-sdk 监听点击空白处关闭bos选择器弹窗
  useEffect(() => {
    const handleClick = (event) => {
      if (!event.isTrusted) return;
      const trigger = document.querySelector('#bos-bucket-select-path') as HTMLElement; // bos选择器
      const popover = document.querySelector('.bos-bucket-select-popover') as HTMLElement; // bos选择器弹窗
      if (!popover || !trigger) return;

      setVisible((prev) => {
        if (prev === true) {
          return false;
        }
        return prev;
      });
    };

    document.addEventListener('click', handleClick);
    return () => {
      document.removeEventListener('click', handleClick);
    };
  }, []);

  return (
    // 阻止冒泡，防止点击组件内部关闭弹窗
    <div onClick={(e) => e.stopPropagation()} className="bos-select-wrapper">
      <BucketSelect
        container="popover"
        target="folder"
        supportMulti={false}
        bucketListApi={client.getBucketList.bind(client)}
        folderListApi={client.getFolderList.bind(client)}
        onSubmit={handleSelect}
        suppertAddFolder={false}
        suppertAddBucket={false}
        value={value}
        visible={visible}
        setVisible={setVisible}
      />
    </div>
  );
};

export default BosSelect;
