/**
 * 处理 x6 数据
 *
 * 将 json 数据格式 转为 x6 数据格式
 * 将 x6 数据格式 转为 json 数据格式
 *    处理 json 节点 位置
 *    处理 json 连线 位置
 *    处理 json 节点 宽高
 *    处理 json 连线 宽高
 *    处理 json 节点 算子
 *    处理 json 连线 算子
 *    处理 json 节点 算子 位置
 *    处理 json 连线 算子 位置
 *    处理 json 节点 算子 宽高
 */

import {NODE_SIZE, X6ShapeType} from '@pages/JobWorkflow/constants';
import {IJsonData, IJsonEdgeData, IJsonNodeData, IX6EdgeData, IX6NodeData} from './type';

const lineAttrs = {
  line: {
    stroke: '#84868C',
    sourceMarker: {
      tagName: 'circle',
      r: 4,
      cx: -2,
      fill: '#84868C',
      strokeWidth: 2,
      stroke: '#fff'
    },
    targetMarker: {
      tagName: 'polyline',
      strokeLinejoin: 'round',
      fill: 'none',
      strokeLinecap: 'round',
      strokeWidth: '2',
      transform: 'scale(1, -1)',
      points:
        '-4.820694099999999 -3.7014096699999994 -0.8355981799999999 0 -4.820694099999999 3.7969886600000002',
      stroke: '#84868C'
    }
  }
};
// 处理 x6 数据到 后端 json 格式
export const dealX6ToJson = (x6Data: (IX6NodeData | IX6EdgeData)[]): IJsonData => {
  const taskRelationList: IJsonEdgeData[] = [];
  const taskDefinitionList: IJsonNodeData[] = [];

  x6Data?.forEach((item) => {
    if (item.shape === X6ShapeType.EDGE) {
      const edge = item as IX6EdgeData;
      taskRelationList.push({
        source: edge.source.cell,
        target: edge.target.cell
      });
    } else {
      const node = item as IX6NodeData;
      taskDefinitionList.push({
        id: node.id,
        name: node.data?.name,
        type: item.shape,
        taskDescription: node.data?.name,
        taskParam: node.data,
        position: node.position
      });
    }
  });

  return {taskRelationList, taskDefinitionList};
};

// 处理 json 节点 位置
const dealJsonPosition = (nodeArr: IJsonNodeData[], edgeArr: IJsonEdgeData[]) => {
  if (!nodeArr || nodeArr.length === 0) {
    return;
  }
  const nodeMap: Map<string, IJsonNodeData> = new Map<string, IJsonNodeData>();
  const beginIdSet: Set<string> = new Set<string>();
  const nextIdMap: Map<string, string[]> = new Map<string, string[]>();
  nodeArr?.forEach((item) => {
    nodeMap.set(item.id, item);
    beginIdSet.add(item.id);
  });
  // 开始节点
  // 处理开始节点 没有被连线的就是开始节点
  edgeArr?.forEach((item) => {
    // 连线必须有效 且 连线不能是同一个节点
    if (nodeMap.has(item.source) && nodeMap.has(item.target) && item.source !== item.target) {
      beginIdSet.delete(item.target);
      nextIdMap.set(item.source, [...(nextIdMap.get(item.source) || []), item.target]);
    }
  });

  if (beginIdSet.size === 0) {
    console.log('存在循环, 默认开始节点为第一个节点');
    beginIdSet.add(nodeArr[0].id);
  }
  let positionY = 0;
  let beginIdArr = Array.from(beginIdSet);
  // 处理节点位置
  while (nodeMap.size > 0) {
    const nextNode = [];
    let nextPositionY = positionY;
    beginIdArr?.forEach((id, index) => {
      const node: IJsonNodeData | undefined = nodeMap.get(id);
      if (node) {
        node.position = {x: 0, y: 0};
        const height =
          (node.operatorList?.length || 0) *
            (NODE_SIZE[X6ShapeType.OPERATOR].height + NODE_SIZE[X6ShapeType.OPERATOR].gutter) +
          NODE_SIZE[X6ShapeType.TASK].height;
        // 处理位置
        node.position.x = index * (NODE_SIZE[X6ShapeType.TASK].width + NODE_SIZE[X6ShapeType.TASK].gutter);
        node.position.y = positionY;
        // 处理宽高
        node.width = NODE_SIZE[X6ShapeType.TASK].width;
        node.height = height;
        // 处理算子位置
        node.operatorList?.forEach((item, index) => {
          item.position = {x: 0, y: 0};
          // 处理宽高位置
          item.width = NODE_SIZE[X6ShapeType.OPERATOR].width;
          item.height = NODE_SIZE[X6ShapeType.OPERATOR].height;
          item.position.x = NODE_SIZE[X6ShapeType.OPERATOR].paddingLeft + node.position.x;
          item.position.y =
            positionY +
            NODE_SIZE[X6ShapeType.TASK].titleHeight +
            index * (NODE_SIZE[X6ShapeType.OPERATOR].height + NODE_SIZE[X6ShapeType.OPERATOR].gutter);
        });

        nodeMap.delete(id);
        nextPositionY = Math.max(nextPositionY, node.position.y + height);
        nextNode.push(...(nextIdMap.get(id) || []));
      }
    });
    beginIdArr = nextNode;
    positionY = nextPositionY + NODE_SIZE[X6ShapeType.TASK].gutter;
  }
};

// 分隔符 避免算子 id 重复
const SPLIT_STR = '-';
// 处理后端 json 格式到 x6 数据
export const dealJsonToX6 = (jsonData: IJsonData): any => {
  const nodeArr: IX6NodeData[] = [];
  const edgeArr: IX6EdgeData[] = [];
  const nodeIdSet: Set<string> = new Set<string>();
  // 算子的位置关系
  const edgeOperatorArr: IX6EdgeData[] = [];
  // 处理task 连线
  jsonData.taskRelationList?.forEach((item) => {
    edgeArr.push({
      shape: X6ShapeType.EDGE,
      source: {cell: item.source},
      target: {cell: item.target},
      attrs: lineAttrs
    });
  });
  // 算子连线关系
  jsonData?.taskDefinitionList?.forEach((item) => {
    item.operatorRelationList?.forEach((operate) => {
      edgeOperatorArr.push({
        shape: X6ShapeType.EDGE,
        source: {cell: item.id + SPLIT_STR + operate.source},
        target: {cell: item.id + SPLIT_STR + operate.target},
        attrs: lineAttrs
      });
    });
  });
  dealJsonPosition(jsonData?.taskDefinitionList, jsonData.taskRelationList);
  // 处理节点
  jsonData?.taskDefinitionList?.forEach((item) => {
    nodeIdSet.add(item.id);
    const operatorArr: IX6NodeData[] = [];
    // 处理算子
    if (item.operatorList) {
      item.operatorList?.forEach((operate) => {
        const operatorId = item.id + '-' + operate.id;
        nodeIdSet.add(operatorId);
        operatorArr.push({
          ...operate,
          id: operatorId,
          shape: X6ShapeType.OPERATOR,
          data: {name: operate.name, param: operate}
        });
      });
    }

    nodeArr.push({
      ...item,
      id: item.id,
      shape: X6ShapeType.TASK,
      data: {parent: true, name: item.name, param: item, type: item.type}
    });
    nodeArr.push(...operatorArr);
  });

  return {
    cells: [
      ...nodeArr,
      ...edgeArr.filter((item) => nodeIdSet.has(item.source.cell) && nodeIdSet.has(item.target.cell)),
      ...edgeOperatorArr.filter((item) => nodeIdSet.has(item.source.cell) && nodeIdSet.has(item.target.cell))
    ]
  };
};
