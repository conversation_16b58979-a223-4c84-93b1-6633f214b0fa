import {FC, useContext, useEffect} from 'react';
import {useNavigate} from 'react-router-dom';
import {getTemplateDetail} from '@api/template';
import {Breadcrumb, Button, Link, Loading, toast} from 'acud';
import useUrlState from '@ahooksjs/use-url-state';
import {useRequest} from 'ahooks';
import urls from '@utils/urls';
import cx from 'classnames';

import styles from './index.module.less';
import X6Page from '@pages/JobWorkflow/Jobs/components/X6Page';
import {WorkflowPageCreateType, WorkflowPageType} from '@pages/JobWorkflow/constants';
import {copyJob} from '@api/job';
import {WorkspaceContext} from '@pages/index';
import IconSvg from '@components/IconSvg';

const TemplateDetail: FC = () => {
  const navigate = useNavigate();
  // 获取工作空间ID
  const {workspaceId} = useContext(WorkspaceContext);

  const [{templateId}] = useUrlState({
    templateId: ''
  });

  const {data, loading} = useRequest(
    () =>
      getTemplateDetail({
        workspaceId,
        templateId
      }),
    {
      refreshDeps: [workspaceId, templateId]
    }
  );

  // TODO: 使用模板 怎么操作
  const handleUseTemplate = async () => {
    const res = await copyJob(
      {
        name: data?.result.templateName,
        code: data?.result.jobTemplate,
        description: data?.result.description,
        workspaceId
      },
      WorkflowPageCreateType.TEMPLATE
    );
    toast.success({message: '创建成功', duration: 5});

    // 导航到编辑页面
    navigate(`${urls.jobDetail}?jobId=${res.result}&edit=true`);
  };

  return (
    <div className={cx(styles['template-detail'], 'db-workspace-wrapper')}>
      <Breadcrumb className={styles['breadcrumb']}>
        <Breadcrumb.Item>
          <Link onClick={() => navigate(`${urls.job}?tabType=${WorkflowPageType.TEMPLATES}`)}>
            工作流列表
          </Link>
        </Breadcrumb.Item>
        <Breadcrumb.Item> </Breadcrumb.Item>
      </Breadcrumb>
      {loading ? (
        <Loading loading>
          <div className={styles['loading-container']} />
        </Loading>
      ) : (
        <>
          <div className={styles['operate']}>
            <div className={styles['name']}>
              <IconSvg className={styles['icon']} type="workflow-template" color="#fff" size={32} />
              {data?.result?.templateName}
            </div>
            <div className={styles['right-options']}>
              {/* <Button onClick={() => window.open(data?.result?.docUrl, '_blank')}>查看文档</Button> */}
              <Button type="primary" onClick={() => handleUseTemplate()}>
                使用模板
              </Button>
            </div>
          </div>
          {/* <div className={styles['workflow-diagram']}>流程图</div> */}

          <div className={styles['workflow-x6']}>
            <X6Page json={data?.result?.jobTemplate} />
          </div>
        </>
      )}
    </div>
  );
};

export default TemplateDetail;
