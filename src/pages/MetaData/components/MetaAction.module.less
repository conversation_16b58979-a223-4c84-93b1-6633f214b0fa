
.meta-action {
  display: flex;
  margin-bottom: 10px;

  &-title {
    flex: 1;
    font-size: 22px;
    font-weight: 500;
    display: inline-block;
    display: flex;
    align-items: center;
    word-break: break-all;
    line-height: 32px;
    margin-right: 16px;

    > span {
      background-image: linear-gradient(180deg, #151B26 0%, #454343 100%);
      border-radius: 8px;
      width: 32px;
      height: 32px;
      line-height: 30px;
      display: inline-flex;
      vertical-align: middle;
      text-align: center;
      margin-right: 8px;
      flex-direction: row;
      align-items: center;
      > span {
        flex: 1;
      }
    }

    svg {
      display: inline-block;
    }
  }

  &-button-group {
    display: inline-flex;
    button {
      padding-left: 8px;
      padding-right: 8px;
    }
  }

  &-button {
    margin-right: 10px;
  }



}
