import React from 'react';
import {Button, Dropdown, Menu} from 'acud';
import {MenuProps} from 'acud/lib/menu/menu';
import {BaseDropdownProps} from 'acud/lib/dropdown/dropdown';
import {Ellipsis} from '@baidu/xicon-react-bigdata';

import './index.less';

interface MenuItem {
  key: string;
  label: string;
  disabled?: boolean;
}
interface MoreButtonProps extends Omit<BaseDropdownProps, 'overlay'> {
  menus: MenuItem[];
  onMenuClick?: MenuProps['onClick'];
}

const MoreButton: React.FC<MoreButtonProps> = ({menus, onMenuClick, ...props}) => {
  const menuItems = menus.map((item) => (
    <Menu.Item key={item.key} disabled={item.disabled}>
      {item.label}
    </Menu.Item>
  ));
  return (
    <Dropdown {...props} overlay={<Menu onClick={onMenuClick}>{menuItems}</Menu>}>
      <Button className="db-more-button" icon={<Ellipsis size={16} />}></Button>
    </Dropdown>
  );
};

export default MoreButton;
