// X6 对象节点数据结构
export interface IX6NodeData {
  id: string;
  shape: string;
  position?: {
    x: number;
    y: number;
  };
  size?: {
    width: number;
    height: number;
  };
  // 大小
  width?: number;
  height?: number;
  // 节点属性配置
  attrs?: object;
  zIndex?: number;
  visible?: boolean;
  // 节点 连接桩配置
  ports?: object;
  // 节点数据配置
  data: any;
}

// X6 连线数据结构
export interface IX6EdgeData {
  id?: string;
  parent?: string;
  shape: string;
  source: IX6NodePortData;
  target: IX6NodePortData;
  attrs?: object;
  zIndex?: number;
  router?: object;
}

// 连接的起点或者 终点
export interface IX6NodePortData {
  cell: string;
  port?: string;
}
// json  边 数据结构
export interface IJsonEdgeData {
  source: string;
  target: string;
}

// 边节点数据结构
export interface IJsonNodeData {
  id: string;
  name: string;
  type: string;
  taskDescription: string;
  taskParam: any;
  position?: {
    x: number;
    y: number;
  };
  width?: number;
  height?: number;
  operatorList?: IJsonOperatorData[];
  operatorRelationList?: IJsonEdgeData[];
}

export interface IJsonOperatorData {
  id: string;
  name: string;
  width?: number;
  height?: number;
  metaData?: {
    operatorName?: string;
  };
  position?: {
    x: number;
    y: number;
  };
}

// json 数据结构
export interface IJsonData {
  taskRelationList: IJsonEdgeData[];
  taskDefinitionList: IJsonNodeData[];
}
