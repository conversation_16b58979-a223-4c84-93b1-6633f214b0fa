/**
 * @file 工作空间详情 - 权限tab页
 * <AUTHOR>
 */

import {FC, useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {Plus1} from '@baidu/xicon-react-bigdata';
import RefreshButton from '@components/RefreshButton';
import {Button, Pagination, Search, Table, toast, Modal, Loading, Space, Tag, Tooltip} from 'acud';
import {ColumnsType} from 'acud/lib/table';
import {Ellipsis} from '@baidu/bce-react-toolkit';
import {useRequest} from 'ahooks';
import CreateModal from './components/CreateModal';
import {
  deleteWorkspaceUser,
  queryWorkspaceUserList,
  IQueryWorkspaceUserListParams,
  IWorkspaceUser,
  IWorkspace
} from '@api/workspace';
import {formatTime} from '@utils/moment';
import {WorkspaceUserTypeMap, WorkspaceRoleMap, EWorkspaceRole} from '../../../constants';
import {OrderType} from '@utils/enums';
import useAuth from '@hooks/useAuth';

import styles from './index.module.less';

interface IWorkspacePermissionTabProps {
  workspaceDetail?: IWorkspace;
  /** 获取当前用户在当前空间的权限 */
  updateWorkspaceAuth: () => void;
}

const WorkspacePermissionTab: FC<IWorkspacePermissionTabProps> = ({workspaceDetail, updateWorkspaceAuth}) => {
  const [workspaceUserList, setWorkspaceUserList] = useState<Array<IWorkspaceUser>>([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [keyword, setKeyword] = useState('');
  const [typeFilterValue, setStatusFilterValue] = useState();
  const [createTimeSortValue, setCreateTimeSortValue] = useState<OrderType>();
  const [currentUser, setCurrentUser] = useState<IWorkspaceUser>();
  const [pagination, setPagination] = useState({
    pageNo: 1,
    pageSize: 10,
    total: 0
  });

  const isSysAdmin = useAuth('global', 'workspace') === 'readWrite';

  // 保证useRequest执行最新的updateWorkspaceAuth
  const updateWorkspaceAuthRef = useRef(updateWorkspaceAuth);

  useEffect(() => {
    updateWorkspaceAuthRef.current = updateWorkspaceAuth;
  }, [updateWorkspaceAuth]);

  // 初始化查询
  useEffect(() => {
    getWorkspaceUserList();
  }, []);

  // 点击新建按钮
  const onClickCreateBtn = useCallback(() => {
    setIsModalVisible(true);
  }, []);

  const {run: runDeleteWorkspaceUser, loading: deleteWorkspaceUserLoading} = useRequest(deleteWorkspaceUser, {
    manual: true,
    onSuccess: (res) => {
      if (res.success) {
        toast.success({message: '删除成功', duration: 5});
        getWorkspaceUserList();
        // 删除成员后，检查当前用户是否还有权限
        updateWorkspaceAuthRef.current();
      }
    }
  });

  const {run: runWorkspaceUserList, loading: queryWorkspaceUserListLoading} = useRequest(
    queryWorkspaceUserList,
    {
      manual: true,
      onSuccess: (res) => {
        if (res.success) {
          setWorkspaceUserList(res?.result?.items || []);
          setPagination((prev) => ({...prev, total: res?.result?.total || 0}));
        }
      }
    }
  );

  // 获取工作空间列表
  const getWorkspaceUserList = useCallback(
    (params: IQueryWorkspaceUserListParams = {}) => {
      if (params.pageNo) {
        setPagination((prev) => ({...prev, pageNo: params.pageNo!}));
      }
      runWorkspaceUserList(workspaceDetail?.id, {
        pageNo: pagination.pageNo,
        pageSize: pagination.pageSize,
        entityName: keyword,
        type: typeFilterValue,
        ...(createTimeSortValue != null
          ? {
              order: createTimeSortValue,
              orderBy: 'createdAt'
            }
          : {}),
        ...params
      });
    },
    [workspaceDetail, runWorkspaceUserList, pagination, keyword, typeFilterValue, createTimeSortValue]
  );

  // 关闭弹窗
  const handleCloseModal = useCallback(() => {
    setCurrentUser(undefined);
    setIsModalVisible(false);
  }, []);

  // 数据总数
  const showTotal = useCallback(() => {
    return `共${pagination.total}条`;
  }, [pagination.total]);

  // 监听点击刷新按钮
  const onClickRefreshBtn = useCallback(() => {
    getWorkspaceUserList();
  }, [getWorkspaceUserList]);

  // 搜索
  const onConfirmSearch = useCallback(
    (value: string) => {
      setKeyword(value);
      getWorkspaceUserList({
        entityName: value,
        pageNo: 1
      });
    },
    [getWorkspaceUserList]
  );

  // 监听表格发生变化
  const onTableChange = useCallback(
    (...args: any) => {
      // 筛选
      const type = args?.[1]?.type?.[0] || null;
      setStatusFilterValue(type);
      // 排序
      let createTimeSortValue = args?.[2]?.order;
      createTimeSortValue =
        createTimeSortValue === 'ascend'
          ? OrderType.asc
          : createTimeSortValue === 'descend'
            ? OrderType.desc
            : null;
      setCreateTimeSortValue(createTimeSortValue);

      getWorkspaceUserList({
        pageNo: 1,
        type,
        ...(createTimeSortValue != null
          ? {
              order: createTimeSortValue,
              orderBy: 'createdAt'
            }
          : {order: undefined, orderBy: undefined})
      });
    },
    [getWorkspaceUserList]
  );

  const columns: ColumnsType<IWorkspaceUser> = useMemo(() => {
    return [
      {
        title: '名称',
        dataIndex: 'entityName',
        key: 'entityName',
        width: '20%',
        ellipsis: {showTitle: false},
        render: (entityName) => {
          return <Ellipsis tooltip={entityName}>{entityName || '-'}</Ellipsis>;
        }
      },
      {
        title: '用户类型',
        dataIndex: 'type',
        key: 'type',
        width: '20%',
        filterMultiple: false,
        filters: Object.keys(WorkspaceUserTypeMap).map((key) => ({
          value: key,
          text: WorkspaceUserTypeMap[key]
        })),
        render: (status) => {
          return <span>{WorkspaceUserTypeMap[status] || '-'}</span>;
        }
      },
      {
        title: '角色',
        dataIndex: 'role',
        key: 'role',
        width: '20%',
        render: (role) => {
          return (
            <Tag className={role === EWorkspaceRole.ADMIN ? styles['admin-tag'] : styles['user-tag']}>
              {WorkspaceRoleMap[role] || '_'}
            </Tag>
          );
        }
      },
      {
        title: '创建时间',
        dataIndex: 'createdAt',
        key: 'createdAt',
        width: '20%',
        sorter: true,
        render: (time) => formatTime(time)
      },
      {
        title: '操作',
        width: '20%',
        fixed: 'right',
        render: (record: IWorkspaceUser) => {
          const deleteDisabled = record?.role === EWorkspaceRole.ADMIN && !isSysAdmin; // 非管理员 不能 删除管理员
          return (
            <Space align="center" style={{gap: 16, height: 20}} className={styles['table-operations']}>
              <Tooltip title={isSysAdmin ? undefined : '暂无此操作权限'}>
                <Button
                  type="actiontext"
                  onClick={() => {
                    setCurrentUser(record);
                    setIsModalVisible(true);
                  }}
                  disabled={!isSysAdmin} // 非管理员 不能 编辑
                >
                  编辑
                </Button>
              </Tooltip>
              <Tooltip title={deleteDisabled ? '暂无此操作权限' : undefined}>
                <Button
                  type="actiontext"
                  onClick={() => {
                    Modal.confirm({
                      title: '删除成员',
                      content: `删除后无法恢复！请确定是否要删除成员“${record.entityName}”`,
                      onOk() {
                        const {entityId, type, role} = record;
                        runDeleteWorkspaceUser(workspaceDetail?.id, {
                          entityId,
                          type,
                          role
                        });
                      },
                      onCancel() {}
                    });
                  }}
                  disabled={deleteDisabled}
                >
                  删除
                </Button>
              </Tooltip>
            </Space>
          );
        }
      }
    ];
  }, [WorkspaceUserTypeMap, workspaceDetail, formatTime, runDeleteWorkspaceUser]);

  return (
    <div className={styles['workspace-permission-tab']}>
      <Loading loading={queryWorkspaceUserListLoading} />
      <div className={styles['operation-container']}>
        <div className={styles['left-container']}>
          <Search
            placeholder="请输入名称进行搜索"
            className={styles['search-container']}
            allowClear
            onSearch={onConfirmSearch}
          />
          <div className={styles['total-count']}>{showTotal()}</div>
        </div>
        <div className={styles['right-container']}>
          <RefreshButton onClick={onClickRefreshBtn}></RefreshButton>
          <Button icon={<Plus1 theme="line" size={16} strokeLinejoin="round" />} onClick={onClickCreateBtn}>
            添加成员
          </Button>
        </div>
      </div>

      <Table
        dataSource={workspaceUserList}
        columns={columns}
        rowKey="entityId"
        scroll={{x: 1228}}
        loading={{
          loading: queryWorkspaceUserListLoading,
          size: 'small'
        }}
        pagination={false}
        onChange={onTableChange}
        className={styles['table-container']}
      />

      {pagination.total > 0 && (
        <div className={styles['pagination-container']}>
          <Pagination
            // showSizeChanger={true}
            // showQuickJumper={true}
            // showTotal={showTotal}
            current={pagination.pageNo}
            total={pagination.total}
            onChange={(page, pageSize = 10) => {
              setPagination((prev) => ({
                ...prev,
                pageNo: page,
                pageSize: pageSize
              }));
              getWorkspaceUserList({
                pageNo: page,
                pageSize: pageSize
              });
            }}
          />
        </div>
      )}
      <CreateModal
        workspaceId={workspaceDetail?.id || ''}
        isModalVisible={isModalVisible}
        currentUser={currentUser}
        handleCloseModal={handleCloseModal}
        getWorkspaceUserList={getWorkspaceUserList}
        updateWorkspaceAuth={updateWorkspaceAuth}
      />
    </div>
  );
};

export default WorkspacePermissionTab;
