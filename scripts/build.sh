#!/usr/bin/env bash

set -e

export PATH=:$PATH

echo "node: $(node -v)"
echo "npm: v$(npm -v)"

export BUILD_ENV="pipeline"


npm ci
if [ "$1" == 'private-common' ]; then
  npm run build:private
else
  npm run build
fi

if [ -d "output" ]; then
    rm -rf output
fi

mkdir -p output

mv dist/* output


if [ "$1" == 'private-common' ]; then


  # 生成版本文件
	echo "$2" >output/_VERSION.js
	echo $(date +"%Y-%m-%d %H:%M:%S") >>output/_VERSION.js
	echo "私有化Databuilder" >>output/_VERSION.js

  cp ./docker/start.sh ./output

fi
