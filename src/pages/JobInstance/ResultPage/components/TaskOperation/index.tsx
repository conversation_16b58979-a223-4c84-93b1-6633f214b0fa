import React, {useContext, useEffect, useMemo, useState} from 'react';
import {
  IJobInstance,
  IJobInstanceTaskNode,
  IJobInstanceTaskOutputResult,
  jobInstanceTaskOutputResult
} from '@api/jobInstance';
import DescriptionList from '@pages/JobInstance/ResultPage/components/DescriptionList';
import IconSvg from '@components/IconSvg';
import styles from './index.module.less';
import {dealOperatorResult} from '../TaskParams/dealParams';
import {Empty, Loading} from 'acud';
import {useMemoizedFn, useRequest} from 'ahooks';
import {WorkspaceContext} from '@pages/index';
// 任务参数组件
const JobInstanceTaskOperation: React.FC<{
  jobInstanceTask?: IJobInstanceTaskNode;
  jobInstance?: IJobInstance;
}> = ({jobInstanceTask, jobInstance}) => {
  const {workspaceId} = useContext(WorkspaceContext);
  // 获取工作空间ID
  const resultArr: any[] = useMemo(() => {
    const arr: any[] = [];
    jobInstanceTask?.operatorList?.forEach((item) => {
      const arrTem = dealOperatorResult(item, jobInstanceTask?.endTime);
      if (arrTem.length === 0) {
        return [];
      }
      arr.push(arrTem);
    });
    return arr;
  }, [jobInstanceTask]);

  // 处理结果 对象转列表
  const dealResult = useMemoizedFn((result: IJobInstanceTaskOutputResult) => {
    return [
      {
        label: '输出来源',
        value: result.operatorName
      },
      {
        label: '算子ID',
        value: result.operatorId
      },
      {
        label: '输出路径',
        value: result.outputPath
      },
      {
        label: '输出格式',
        value: result.outputFormat
      },
      {
        label: '生成时间',
        value: result.generateTime
      }
    ];
  });
  // 查询结果
  const {
    run: runQueryResult,
    data: result,
    loading
  } = useRequest(
    () =>
      jobInstanceTaskOutputResult(workspaceId, {
        jobInstanceId: jobInstance?.jobInstanceId,
        taskId: jobInstanceTask?.id,
        subTaskCode: jobInstanceTask?.subTaskCode
      }),
    {
      manual: true
    }
  );
  // 查询 任务或者子任务变化 重新查询
  useEffect(() => {
    if (jobInstanceTask?.id && jobInstanceTask?.subTaskCode) {
      runQueryResult();
    }
  }, [jobInstanceTask?.id, jobInstanceTask?.subTaskCode]);

  return (
    <div className={styles['task-operation-item']}>
      {/* {resultArr?.map((item, index) => {
        return (
          <div key={index} className={styles['operation']}>
            <div className={styles['title']}>
              <IconSvg type="workflow-task-result" size={16} />
              <span>任务结果{++index}</span>
            </div>
            <div className={styles['list']}>
              <DescriptionList infoList={item} />
            </div>
          </div>
        );
      })} */}

      <Loading loading={loading}></Loading>
      {result?.result?.operatorOutputs?.map((item, index) => {
        return (
          <div key={index} className={styles['operation']}>
            <div className={styles['title']}>
              <IconSvg type="workflow-task-result" size={16} />
              <span>任务结果{++index}</span>
            </div>
            <div className={styles['list']}>
              <DescriptionList infoList={dealResult(item)} />
            </div>
          </div>
        );
      })}

      {(!result || result?.result?.operatorOutputs?.length === 0) && <Empty description="暂无结果" />}
    </div>
  );
};

export default JobInstanceTaskOperation;
