import React, {useState} from 'react';
import {Select} from 'acud';
import {useRequest} from 'ahooks';
import {isArray} from 'lodash';
/**
 * 自定义远程下拉框，支持 Select 所有参数
 * @param queryList 查询列表 传递分页方法
 * @param id select 下拉框 对应列表字段  的 value
 * @param name select 下拉框 对应列表字段 的 label
 * @param params 查询参数
 */
interface IRemoteSelectProps extends React.ComponentProps<typeof Select> {
  queryList: (...args: any[]) => Promise<any>;
  objId?: string;
  objName?: string;
  params?: any[];
}
const RemoteSelect: React.FC<IRemoteSelectProps> = ({
  queryList,
  objId = 'id',
  objName = 'name',
  params = [],
  ...rest
}) => {
  const [list, setList] = useState<{value: string; label: string}[]>([]);
  // 查询并且设置下拉框数据
  const {loading} = useRequest(() => queryList(...params), {
    onSuccess: (res) => {
      const {result} = res;
      Object.keys(result).forEach((key) => {
        // 使用分页接口的数组数据
        if (isArray(result[key])) {
          const arr = [];
          // 去重 避免id 重复
          const set = new Set();
          result[key].forEach((item) => {
            if (!set.has(item[objId])) {
              set.add(item[objId]);
              arr.push({value: item[objId], label: item[objName]});
            }
          });
          setList(arr);
        }
      });
    }
  });

  return <Select {...rest} loading={loading} options={list} style={{width: '100%'}} />;
};

export default RemoteSelect;
