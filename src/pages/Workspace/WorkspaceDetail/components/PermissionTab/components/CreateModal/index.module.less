.workspace-user-modal {
  overflow-y: visible;

  .workspace-user-modal-form-list {
    max-height: 400px;
    overflow-y: auto;
  }

  .workspace-user-modal-form-item {
    display: flex;
    flex-flow: row nowrap;
    align-items: flex-start;

    :nth-child(1),
    :nth-child(2) {
      flex: 1;
    }

    :last-child > button {
      margin-top: 24px;
    }
  }

  &-add-form-item {
    height: 20px;

    :global {
      .acud-form-item-control-input {
        min-height: 20px;
      }
    }

    .workspace-user-modal-add-btn,
    .workspace-user-modal-add-btn button {
      font-family: PingFangSC-Medium;
      font-size: 12px;
      line-height: 20px;
      height: 20px;
      border: none;
      padding: 0;
      min-width: 0;
    }
  }

  &-user-select-dropdown {
    :global {
      .acud-select-item-option-disabled {
        div {
          color: #b8babf !important;
        }
      }
    }
  }

  &-form-item-user-option {
    font-family: PingFangSC-Regular;
    font-size: 12px;
    display: flex;
    justify-content: space-between;

    &-name {
      color: #151b26;
    }

    &-tag {
      color: #84868c;
      flex: unset !important;
    }
  }

  :global {
    .acud-modal-content {
      .acud-modal-body {
        margin-bottom: 0;
        overflow-y: hidden;
      }

      .acud-form-item {
        margin-bottom: 0;

        .acud-form-item-label {
          font-family: PingFangSC-Medium;
          font-size: 12px;
          color: #151b26;
          line-height: 20px;
        }

        .acud-form-item-control {
          max-width: unset !important;
        }

        .acud-form-item-extra {
          font-family: PingFangSC-Regular;
        }
      }
    }
  }
}
