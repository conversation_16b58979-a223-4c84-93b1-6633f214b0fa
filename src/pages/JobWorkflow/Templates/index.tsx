import {useState, FC, useRef, useContext} from 'react';
import {useRequest} from 'ahooks';
import {queryTemplateList, ITemplate} from '@api/template';
import {Button, Loading, Pagination, Search, toast} from 'acud';
import RefreshButton from '@components/RefreshButton';
import {useNavigate} from 'react-router-dom';
import styles from './index.module.less';
import urls from '@utils/urls';
import {WorkspaceContext} from '@pages/index';
import {copyJob, createJob} from '@api/job';
import {WorkflowPageCreateType} from '../constants';
import IconSvg from '@components/IconSvg';
import templateImg from '@assets/png/workflow/template.png';

const TemplateItem: FC<{item: ITemplate}> = ({item}) => {
  const navigate = useNavigate();
  const {workspaceId} = useContext(WorkspaceContext);

  // 使用模板 创建工作流
  const handleUseTemplate = async () => {
    const res = await copyJob(
      {
        name: item.templateName,
        code: item.jobTemplate,
        description: item.description,
        workspaceId
      },
      WorkflowPageCreateType.TEMPLATE
    );
    toast.success({message: '创建成功', duration: 5});

    // 导航到编辑页面
    navigate(`${urls.jobDetail}?jobId=${res.result}&edit=true`);
  };

  return (
    <div
      className={styles['template-item']}
      onClick={() => navigate(`${urls.templateDetail}?templateId=${item.templateId}`)}
    >
      <div className={styles['template-item-header']}>
        <IconSvg className={styles['icon']} type="workflow-template" color="#fff" size={24} />
        {item.templateName}
      </div>
      <div className={styles['template-item-img']}>
        <div
          className={styles['template-item-img-bg']}
          style={{backgroundImage: `url(${item.templateThumb || templateImg})`}}
        ></div>
      </div>
      <div className={styles['template-item-container']}>
        <div className={styles['template-item-container-description']}>{item.description}</div>
        <div className={styles['template-item-container-options']}>
          <Button
            type="primary"
            onClick={(e) => {
              handleUseTemplate();
              e.stopPropagation();
            }}
          >
            使用模板
          </Button>
          {/* <Button
            onClick={(e) => {
              window.open(item.docUrl, '_blank');
              e.stopPropagation();
            }}
          >
            查看文档
          </Button> */}
        </div>
      </div>
    </div>
  );
};

const Templates: FC = () => {
  // 查询字段
  const [keyword, setKeyword] = useState('');

  // 默认支持名称查询
  const [keywordType] = useState('templateName');

  const {workspaceId} = useContext(WorkspaceContext);
  // 分页字段
  const [pagination, setPagination] = useState<{
    pageNo: number;
    pageSize?: number;
  }>({
    pageNo: 1,
    pageSize: 10
  });

  const {
    data: dataSource,
    loading,
    run: runQueryJobList
  } = useRequest(() => queryTemplateList(workspaceId, {...pagination, keyword, keywordType}), {
    refreshDeps: [pagination] //  分页 和 排序变化时刷新
  });

  return (
    <div className={styles['template-container']}>
      <div className={styles['operation-container']}>
        {/* <span className={styles['total-count']}>共{dataSource?.result?.totalCount || 0}条</ span> */}
        <div className="w-[240px]">
          <Search
            placeholder="请输⼊模板名称进⾏搜索"
            className={styles['search-container']}
            allowClear
            onSearch={() => setPagination((obj) => ({...obj, pageNo: 1}))}
            onChange={(e) => setKeyword(e.target.value)}
          />
        </div>
        <div className={styles['right-container']}>
          <RefreshButton onClick={runQueryJobList}></RefreshButton>
        </div>
      </div>
      {loading ? (
        <Loading loading>
          <div className={styles['loading-container']} />
        </Loading>
      ) : (
        <>
          <div className={styles['templates-container']}>
            {dataSource?.result.result.map((item) => <TemplateItem key={item.templateId} item={item} />)}
          </div>
          <div className={styles['pagination-container']}>
            <Pagination
              showSizeChanger
              showQuickJumper
              current={pagination.pageNo}
              pageSize={pagination.pageSize}
              total={dataSource?.result?.totalCount || 0}
              onChange={(page, pageSize) => {
                setPagination({
                  pageNo: page,
                  pageSize: pageSize
                });
              }}
            />
          </div>
        </>
      )}
    </div>
  );
};

export default Templates;
