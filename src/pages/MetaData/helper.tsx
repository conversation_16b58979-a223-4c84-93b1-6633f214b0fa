import {DataNode} from 'acud/lib/tree';
import {IUrlStateHandler} from './index';

import * as http from '@api/metaRequest';
import TextEllipsis from '@components/TextEllipsisTooltip';

/**
 * 根据 url 参数决定使用哪种 Panel
 * @param urlState  IUrlStateHandler['urlState']
 * @returns 'catalog' | 'schema' | 'table' | 'volume' | 'operator' | null
 */
export function determineNodeType(
  urlState: IUrlStateHandler['urlState']
): 'catalog' | 'schema' | 'table' | 'volume' | 'operator' | null {
  const {catalog, schema, type, node} = urlState;

  if (!!catalog && !schema && !type && !node) return 'catalog';
  if (!!catalog && !!schema && !type && !node) return 'schema';
  if (!!catalog && !!schema && type === 'table' && !!node) return 'table';
  if (!!catalog && !!schema && type === 'volume' && !!node) return 'volume';
  if (!!catalog && !!schema && type === 'operator' && !!node) return 'operator';

  return null;
}

// 根据文件名称 获取文件图标类型和颜色
export const getIconTypeFromName = (name: string) => {
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'];
  if (imageExtensions.some((ext) => name.toLowerCase().endsWith(ext))) return ['metadata-picture', '#FFAB52'];
  if (name.endsWith('.txt')) return ['metadata-txt', '#2468F2'];
  if (name.endsWith('.pdf')) return ['metadata-pdf', '#E62E6B'];
  if (name.endsWith('.docx')) return ['metadata-docx', '#2468F2'];
  if (name.endsWith('.xlsx')) return ['metadata-xlsx', '#30BF13'];
  if (name.endsWith('.pptx')) return ['metadata-pptx', '#E62E6B'];
  if (name.endsWith('.js')) return ['metadata-js', '#7073FF'];
  return ['metadata-file', '#2468F2'];
};

/**
 * 根据 url 参数生成节点的选中和展开状态
 * @param urlState IUrlStateHandler['urlState']
 * @returns {selectedKeys: string[] | null, expandedKeys: string[]}
 * selectedKeys: 当前选中的节点 key
 * expandedKeys: 当前展开的节点 key 数组
 */
export function joinNodeKey(urlState: IUrlStateHandler['urlState']) {
  const {catalog, schema, type, node} = urlState;

  if (!catalog) return {selectedKeys: null, expandedKeys: []};

  const baseKey = `${catalog}`;
  const schemaKey = `${baseKey}#>${schema}`;
  const typeKey = `${schemaKey}#>${type}`;
  const nodeKey = `${typeKey}#>${node}`;

  if (!schema && !type && !node) {
    return {
      selectedKeys: baseKey,
      expandedKeys: []
    };
  }

  if (schema && !type && !node) {
    return {
      selectedKeys: schemaKey,
      expandedKeys: [baseKey]
    };
  }

  if (schema && type && node) {
    return {
      selectedKeys: nodeKey,
      expandedKeys: [baseKey, schemaKey, typeKey]
    };
  }

  return {selectedKeys: null, expandedKeys: []};
}

// 新建弹窗 & 重命名弹窗： Form 表单的布局属性配置
export const formItemLayout = {
  labelCol: {
    span: 5
  },
  wrapperCol: {
    span: 7
  },
  labelAlign: 'left' as 'left' | 'right'
};

/**
 * 处理 http.searchMetastore 请求的响应数据，转为 acud Tree 组件 treeData 属性可用的值
 * @param data http.ISearchMetastoreRes[]
 * @returns DataNode[]
 */
export function transformToacudTreeData(data: http.ISearchMetastoreRes[], keyword: string): DataNode[] {
  const buildTree = (node, parentKey = '', keyword: string) => {
    if (node.name) {
      const title = (
        <TextEllipsis
          width={'calc(100% - 24px)'}
          tooltip={node.name}
          keyword={keyword}
          keywordColor="#2468F2"
        >
          {node.name}
        </TextEllipsis>
      );
      const key = parentKey ? `${parentKey}#>${node.name}` : node.name;
      const children = [];

      if (node.schemas) {
        node.schemas.forEach((schema) => {
          const schemaNode = {
            title: (
              <TextEllipsis
                width={'calc(100% - 24px)'}
                tooltip={schema.name}
                keyword={keyword}
                keywordColor="#2468F2"
              >
                {schema.name}
              </TextEllipsis>
            ),
            key: `${key}#>${schema.name}`,
            children: []
          };

          if (schema.tables) {
            const tablesNode = {
              title: 'Table',
              key: `${schemaNode.key}#>table`,
              children: schema.tables.map((table) => ({
                title: (
                  <TextEllipsis
                    width={'calc(100% - 24px)'}
                    tooltip={table.name}
                    keyword={keyword}
                    keywordColor="#2468F2"
                  >
                    {table.name}
                  </TextEllipsis>
                ),
                key: `${schemaNode.key}#>table#>${table.name}`,
                isLeaf: true
              }))
            };
            schemaNode.children.push(tablesNode);
          }

          if (schema.volumes) {
            const volumesNode = {
              title: 'Volume',
              key: `${schemaNode.key}#>volume`,
              children: schema.volumes.map((volume) => ({
                title: (
                  <TextEllipsis
                    width={'calc(100% - 24px)'}
                    tooltip={volume.name}
                    keyword={keyword}
                    keywordColor="#2468F2"
                  >
                    {volume.name}
                  </TextEllipsis>
                ),
                key: `${schemaNode.key}#>volume#>${volume.name}`,
                isLeaf: true
              }))
            };
            schemaNode.children.push(volumesNode);
          }

          if (schema.operators) {
            const operatorsNode = {
              title: 'Operator',
              key: `${schemaNode.key}#>operator`,
              children: schema.operators.map((operator) => ({
                title: (
                  <TextEllipsis
                    width={'calc(100% - 24px)'}
                    tooltip={operator.name}
                    keyword={keyword}
                    keywordColor="#2468F2"
                  >
                    {operator.name}
                  </TextEllipsis>
                ),
                key: `${schemaNode.key}#>operator#>${operator.name}`,
                isLeaf: true
              }))
            };
            schemaNode.children.push(operatorsNode);
          }

          children.push(schemaNode);
        });
      }

      return {title, key, children};
    }
    return null;
  };

  const acudTreeData = [];
  data.forEach((item) => {
    const treeNode = buildTree(item, undefined, keyword);
    if (treeNode) {
      acudTreeData.push(treeNode);
    }
  });

  return acudTreeData;
}
