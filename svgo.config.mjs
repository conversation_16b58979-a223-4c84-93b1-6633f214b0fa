// 自定义插件，修改 stroke 属性
const editStroke = {
  name: 'editStroke',
  fn: () => {
    return {
      // TODO: 需要优化 判断是否需要压缩
      // root: {
      //   enter: (node) => {
      //     // console.log('enter', node);
      //     // if (node?.attributes?.['optimized']) {
      //     //   return false;
      //     // }
      //   },
      //   exit: (node) => {
      //     node.attributes.optimized = true;
      //   }
      // },
      element: {
        enter: (node) => {
          // if (node.attributes.stroke === '#151B26') {
          if (node.attributes.stroke) {
            node.attributes.stroke = 'currentColor';
            return;
          }
        }
      }
    };
  }
};

export default {
  js2svg: {
    pretty: false // 表示禁用格式化（例如，移除不必要的换行和空格），使 SVG 输出为一行。
  },
  plugins: [
    'preset-default', // 这个插件是 SVGO 的默认插件组合，启用后会自动执行一系列常见的优化操作（例如，删除不必要的属性、压缩路径数据等）。
    {
      name: 'removeDimensions', // 删除 width 和 height 属性
      params: {
        removeWidth: true,
        removeHeight: true
      }
    },
    editStroke
  ]
};
