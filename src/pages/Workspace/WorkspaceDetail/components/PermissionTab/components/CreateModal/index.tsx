/**
 * @file 工作空间 - 新建弹窗
 * <AUTHOR>
 */

import {FC, useCallback, useEffect, useMemo, useState} from 'react';
import {Form, Select, Input, Modal, toast, Space, Button, Tooltip, Loading} from 'acud';
import {Plus1, Delete} from '@baidu/xicon-react-bigdata';
import {IQueryWorkspaceUserListParams, IWorkspaceUser} from '@api/workspace';
import {
  WorkspaceRoleMap,
  EWorkspaceRole,
  EWorkspaceUserType,
  WorkspaceUserTypeMap
} from '../../../../../constants';
import {
  queryIamUserList,
  queryIamUserGroupList,
  IIamUser<PERSON>,
  queryWorkspaceUserList,
  createWorkspaceUser,
  updateWorkspaceUser
} from '@api/workspace';
import useAuth from '@hooks/useAuth';
import styles from './index.module.less';
const {Option} = Select;

const PREFIX = 'workspace-user-modal';
// 添加用户数量限制
const MAX_USER_LIMIT = 10;
interface IUserOptions extends IIamUsers {
  type: EWorkspaceUserType;
  disabled: boolean;
}

interface ICreateModalProps {
  workspaceId: string;
  currentUser?: IWorkspaceUser;
  isModalVisible: boolean;
  getWorkspaceUserList: (params?: IQueryWorkspaceUserListParams) => void;
  handleCloseModal: () => void;
  /** 获取当前用户在当前空间的权限 */
  updateWorkspaceAuth: () => void;
}

const CreateModal: FC<ICreateModalProps> = ({
  workspaceId,
  isModalVisible,
  currentUser,
  getWorkspaceUserList,
  handleCloseModal,
  updateWorkspaceAuth
}) => {
  const [form] = Form.useForm();
  const formItems = Form.useWatch('items', form);
  const [loading, setLoading] = useState(false);
  const [userListLoading, setUserListLoading] = useState(true);
  const [userList, setUserList] = useState<IUserOptions[]>();
  const [selectedUserIds, setSelectUserIds] = useState<string[]>();
  const isCreate = !currentUser;

  const isSysAdmin = useAuth('global', 'workspace') === 'readWrite';

  // 打开弹窗时，获取最新用户和用户组列表
  useEffect(() => {
    if (isModalVisible) {
      Promise.all([
        queryIamUserList(),
        queryIamUserGroupList(),
        queryWorkspaceUserList(workspaceId, {
          pageNo: 1,
          pageSize: 10000
        })
      ])
        .then(([usersRes, groupsRes, workspaceUsersRes]) => {
          if (usersRes?.success && groupsRes?.success && workspaceUsersRes?.success) {
            const workspaceUserList = workspaceUsersRes.result?.items || [];
            const userList =
              usersRes?.page.result?.map((item: IIamUsers) => ({
                id: item.id,
                name: item.name,
                type: EWorkspaceUserType.USER,
                disabled: workspaceUserList.some(
                  (workspaceUser: IWorkspaceUser) => workspaceUser.entityId === item.id
                )
              })) || [];
            const groupList =
              groupsRes?.page.result?.map((item: IIamUsers) => ({
                id: item.id,
                name: item.name,
                type: EWorkspaceUserType.GROUP,
                disabled: workspaceUserList.some(
                  (workspaceUser: IWorkspaceUser) => workspaceUser.entityId === item.id
                )
              })) || [];
            setUserList([...userList, ...groupList]);
          }
        })
        .finally(() => {
          setUserListLoading(false);
        });
    }
  }, [isModalVisible, queryIamUserList, queryIamUserGroupList, queryWorkspaceUserList]);

  // 编辑时，初始化表单数据
  useEffect(() => {
    if (currentUser) {
      form.setFieldValue('items', [currentUser]);
    }
  }, [currentUser, form]);

  // 收集已选择的用户/用户组id，用于禁用select option，禁止重复选择
  useEffect(() => {
    setSelectUserIds(formItems?.map((item) => item?.entityId));
  }, [formItems]);

  // 关闭弹窗
  const onCloseModal = useCallback(() => {
    handleCloseModal();
    form.resetFields();
  }, [form, handleCloseModal]);

  // 提交表单
  const handleConfirm = useCallback(() => {
    if (loading) {
      return;
    }
    form
      .validateFields()
      .then((values) => {
        let params: any = {
          items: values.items.map((item) => {
            const curUser = userList?.find((user) => user.id === item.entityId);
            return {
              type: curUser?.type,
              entityId: curUser?.id,
              role: item.role,
              entityName: curUser?.name
            };
          })
        };
        params = isCreate ? params : params.items[0];
        const request = isCreate ? createWorkspaceUser : updateWorkspaceUser;

        setLoading(true);
        request(workspaceId, params)
          .then((res) => {
            if (res?.success) {
              toast.success({
                message: !currentUser ? '创建成功' : '编辑成功',
                duration: 5
              });

              onCloseModal();
              getWorkspaceUserList({
                pageNo: 1
              });
              updateWorkspaceAuth();
            }
          })
          .finally(() => {
            setLoading(false);
          });
      })
      .catch(() => {});
  }, [
    form,
    loading,
    userList,
    isCreate,
    workspaceId,
    createWorkspaceUser,
    updateWorkspaceUser,
    onCloseModal,
    getWorkspaceUserList,
    updateWorkspaceAuth
  ]);

  // 添加form-item后滚动到最底部
  const autoScroll = () => {
    setTimeout(() => {
      const scrollWrapper = document.querySelector(`.${styles[`${PREFIX}-form-list`]}`);
      scrollWrapper?.scrollTo({top: scrollWrapper?.scrollHeight, behavior: 'smooth'});
    }, 100);
  };

  return (
    <Modal
      closable={true}
      title={!currentUser ? '添加成员' : '编辑成员'}
      width={600}
      visible={isModalVisible}
      onOk={handleConfirm}
      onCancel={onCloseModal}
      okButtonProps={{
        loading
      }}
      destroyOnClose={true}
      className={styles[PREFIX]}
    >
      {userListLoading ? ( // 避免用户列表未返回时, 匹配不到用户名出现用户id
        <Loading loading={true}></Loading>
      ) : (
        <Form
          labelAlign="left"
          layout="vertical"
          colon={false}
          labelWidth={80}
          initialValues={{items: [{}]}}
          form={form}
        >
          <Form.List name="items">
            {(fields, {add, remove}) => (
              <>
                <div className={styles[`${PREFIX}-form-list`]}>
                  {fields.map(({key, name, ...restField}) => (
                    <Space
                      key={key}
                      style={{
                        display: 'flex',
                        marginBottom: 12
                      }}
                      align="baseline"
                      className={styles[`${PREFIX}-form-item`]}
                    >
                      <Form.Item
                        {...restField}
                        label="用户/用户组"
                        name={[name, 'entityId']}
                        rules={[
                          {
                            required: true,
                            message: '请选择用户/用户组'
                          }
                        ]}
                      >
                        <Select
                          placeholder="请选择用户/用户组"
                          style={{width: '100%'}}
                          disabled={!isCreate}
                          dropdownClassName={styles[`${PREFIX}-user-select-dropdown`]}
                          showSearch
                          filterOption={(inputValue, option) =>
                            option?.title.toString().toLowerCase().indexOf(inputValue.toLowerCase()) >= 0
                          }
                        >
                          {userList?.map((user) => (
                            <Option
                              value={user.id}
                              key={user.id}
                              title={user.name}
                              disabled={user.disabled || selectedUserIds?.includes(user.id)}
                            >
                              <div className={styles[`${PREFIX}-form-item-user-option`]}>
                                <div className={styles[`${PREFIX}-form-item-user-option-name`]}>
                                  {user.name}
                                </div>
                                <div className={styles[`${PREFIX}-form-item-user-option-tag`]}>
                                  {WorkspaceUserTypeMap[user.type]}
                                </div>
                              </div>
                            </Option>
                          ))}
                        </Select>
                      </Form.Item>
                      <Form.Item
                        {...restField}
                        label="角色"
                        name={[name, 'role']}
                        rules={[
                          {
                            required: true,
                            message: '请选择角色'
                          }
                        ]}
                      >
                        <Select placeholder="请选择角色" style={{width: '100%'}}>
                          {Object.keys(WorkspaceRoleMap).map((key) => (
                            <Option
                              value={key}
                              key={key}
                              disabled={key === EWorkspaceRole.ADMIN && !isSysAdmin} // 系统管理员才可以设置空间管理员
                            >
                              {WorkspaceRoleMap[key]}
                            </Option>
                          ))}
                        </Select>
                      </Form.Item>
                      <Button
                        onClick={() => remove(name)}
                        disabled={fields.length === 1}
                        icon={<Delete theme="line" size={16} strokeLinejoin="round" />}
                      />
                    </Space>
                  ))}
                </div>
                {!currentUser && (
                  <Form.Item className={styles[`${PREFIX}-add-form-item`]}>
                    <Tooltip
                      title={
                        fields.length >= MAX_USER_LIMIT ? `单次最多可添加${MAX_USER_LIMIT}个用户/用户组` : ''
                      }
                    >
                      <Button
                        className={styles[`${PREFIX}-add-btn`]}
                        type="actiontext"
                        icon={<Plus1 theme="line" size={16} strokeLinejoin="round" />}
                        onClick={() => {
                          add();
                          autoScroll();
                        }}
                        disabled={fields.length >= MAX_USER_LIMIT}
                      >
                        添加
                      </Button>
                    </Tooltip>
                  </Form.Item>
                )}
              </>
            )}
          </Form.List>
        </Form>
      )}
    </Modal>
  );
};

export default CreateModal;
