/**
 * 删除弹窗： Catalog、Schema、Table、Volume、Operator
 * * 注意：
 * * * 1、本文件导出是 Function 非 Component
 * <AUTHOR>
 */

import {Modal, toast} from 'acud';
import {deleteCatalog, deleteSchema, deleteTable, deleteVolume, deleteOperator} from '@api/metaRequest';

type IrequestFun =
  | typeof deleteCatalog
  | typeof deleteSchema
  | typeof deleteTable
  | typeof deleteVolume
  | typeof deleteOperator;

interface IModalRemoveParams {
  title: string; // 弹窗标题
  content?: string; // 弹窗提醒内容
  requestFun: IrequestFun; // 点击确定创建的请求方法
  fullName: string; // catalog 全名。{metastoreId}.{catalogName}
  name: string; // Catalog 名称
  successFun: (...args: any[]) => any; // requestFun 请求成功后回调
}

const ModalRemoveFun = (params: IModalRemoveParams) => {
  const {title, content, requestFun, fullName, name, successFun} = params;

  const handleOk = async () => {
    const res = await requestFun(fullName);
    if (res?.success != true) {
      throw new Error();
    }
    toast.success({
      message: '删除成功！',
      duration: 5
    });
  };

  Modal.confirm({
    title: `删除 ${title}`,
    content: content ? content : `删除后⽆法恢复！请确定是否要删除 “${name}”`,
    okText: '删除',
    onOk() {
      return handleOk().then(() => successFun());
    }
  });
};

export default ModalRemoveFun;
