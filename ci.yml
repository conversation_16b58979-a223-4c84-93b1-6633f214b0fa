Global:
  version: '2.0'
  group_email: <EMAIL>
Default:
  profile:
    - buildProduction
Profiles:
  - profile:
    name: buildProduction
    mode: AGENT
    environment:
      image: DECK_STD_CENTOS7
      tools:
        - nodejs: 22.latest
    build:
      command: sh scripts/build.sh
      cache:
        enable: true
        paths:
          - node_modules
    artifacts:
      release: true
  - profile:
    name: build_DatabuilderPrivate
    mode: AGENT
    environment:
      image: DECK_STD_CENTOS7
      tools:
        - nodejs: 22.latest
    build:
      command: sh scripts/build.sh private-common ${AGILE_COMPILE_BRANCH}
      cache:
        enable: true
        paths:
          - node_modules
    artifacts:
      release: true
