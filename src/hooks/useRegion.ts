/**
 * COPY FROM @baidu/bce-react-toolkit 包中 src/hooks/useRegion.tsx 文件
 * 为兼容DB产品私有化，处理解决
 * <AUTHOR>
 */
import {useCallback, useEffect, useMemo, useState} from 'react';

interface InitialValue {
  onRegionChange?: (region?: string) => void;
}

export interface Region {
  id: string;
  rawId: string;
  label: string;
  isGlobal: boolean;
  datasource: Array<{
    id: string;
    rawId: string;
    text: string;
    value: string;
  }>;
}

export enum FrameworkEvents {
  ACTION_NOT_FOUND = 'actionnotfound',
  FORWARD_ACTION = 'forwardaction',
  ENTER_ACTION_COMPLETE = 'enteractioncomplete',
  LEAVE_ACTION = 'leaveaction',
  SERVICES_CHANGED = 'services_changed',
  REGION_CHANGED = 'region_changed',
  AFTER_REGION_CHANGED = 'after_region_changed',
  HIDE_REGION_SWITCHER = 'hide_region_switcher',
  SET_REGION_RESOURCE_NUM = 'set_resource_num'
}

export function useRegion(initialValue?: InitialValue) {
  const {onRegionChange} = initialValue || {};
  const [currentRegion, setCurrentRegion] = useState<Region>(window?.$framework?.region?.getCurrentRegion());

  const region = useMemo(() => {
    return currentRegion?.id;
  }, [currentRegion?.id]);

  const handleRegionChange = useCallback(() => {
    const value = window?.$framework?.region?.getCurrentRegion();
    setCurrentRegion(value);
    onRegionChange?.(value?.id);
  }, [onRegionChange]);

  const setRegion = useCallback((region: string) => {
    return window?.$framework?.region?.setRegion(region);
  }, []);

  useEffect(() => {
    window?.$framework?.events?.on(FrameworkEvents.AFTER_REGION_CHANGED, handleRegionChange);

    return () => {
      window?.$framework?.events?.un(FrameworkEvents.AFTER_REGION_CHANGED, handleRegionChange);
    };
  }, [handleRegionChange]);

  return {currentRegion, region, setRegion};
}
