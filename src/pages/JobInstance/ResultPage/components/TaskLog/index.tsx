import React, {useContext, useEffect, useState} from 'react';
import {IJobInstance, IJobInstanceTaskNode, jobInstanceTaskLog} from '@api/jobInstance';
import {useMemoizedFn, useRequest} from 'ahooks';
import {Col, Empty, Loading, Pagination, Row} from 'acud';
import {WorkspaceContext} from '@pages/index';
import styles from './index.module.less';
import MonacoLog from '@pages/JobInstance/components/MonacoLog';
// 任务参数组件
const JobInstanceTaskLog: React.FC<{
  jobInstanceTask?: IJobInstanceTaskNode;
  jobInstance?: IJobInstance;
  logRefreshTime?: number;
}> = ({jobInstanceTask, jobInstance, logRefreshTime}) => {
  const {workspaceId} = useContext(WorkspaceContext);
  // 自动刷新
  const [taskFinished, setTaskFinished] = useState(false);
  // 当前页码
  const [pageNo, setPageNo] = useState<number>(1);

  const {
    data: logData,
    run: runQueryLog,
    loading
  } = useRequest(jobInstanceTaskLog, {
    manual: true,
    pollingInterval: taskFinished ? undefined : 5000, // 每 3s 轮询，flag 为 false 时停止
    pollingWhenHidden: false, // 页面不可见时暂停轮询
    onSuccess: (res) => {
      // 如果请求失败，取消刷新
      if (!res.success) {
        setTaskFinished(true);
        return;
      }
      // 如果当前页码为 0，则设置为总页码 最新的数据
      if (pageNo === 0) {
        setPageNo(res.result.totalLogPageCount);
      }
      const result = res.result;
      setTaskFinished(!!result.taskFinished);
    }
  });
  // 查询日志
  const queryLog = useMemoizedFn((pageNo?: number) => {
    if (!pageNo) {
      setPageNo(0);
    }
    if (!jobInstanceTask?.subTaskCode) {
      return;
    }
    runQueryLog(workspaceId, {
      jobInstanceId: jobInstance?.jobInstanceId,
      taskId: jobInstanceTask?.id,
      subTaskCode: jobInstanceTask?.subTaskCode,
      pageNo
    });
  });

  const changePageNo = useMemoizedFn((pageNo: number) => {
    setPageNo(pageNo);
    queryLog(pageNo);
  });
  useEffect(() => {
    queryLog();
  }, [jobInstanceTask]);

  // 日志刷新
  useEffect(() => {
    if (logRefreshTime) {
      queryLog();
    }
  }, [logRefreshTime]);

  return (
    <div className={styles['task-log-container']}>
      {jobInstanceTask?.subTaskCode ? (
        <>
          <div className={styles['content']}>
            <MonacoLog value={logData?.result?.logContent || ''} loading={loading} />
          </div>
          <div className={styles['pagination']}>
            <Pagination
              className="flex justify-end"
              size="small"
              pageSize={1}
              total={logData?.result?.totalLogPageCount || 0}
              current={pageNo}
              onChange={changePageNo}
            />
          </div>
        </>
      ) : (
        <Empty description="任务未执行" />
      )}
    </div>
  );
};

export default JobInstanceTaskLog;
