.bos-select-wrapper {
  .bos-bucket-select-breadcrumb {
    & > div {
      width: 100%;
      white-space: nowrap;
    }
    .bos-bucket-select-breadcrumb-btn {
      max-width: calc((100% - 12px - 32px - 83px - 32px) / 4); // bos sdk有问题，手动计算宽度，避免超出
    }
  }
  .bos-bucket-select-popover {
    width: 100%;

    .acud-popover-inner .acud-popover-inner-content .bos-bucket-select {
      .bos-bucket-select-keyword-search {
        flex: 1;
      }
    }

    .bos-bucket-select-container {
      width: 100%;

      .bos-bucket-select-option-name {
        // 去除region标识
        & > span {
          display: none;
        }
      }
    }
  }
}
