import {Loading} from 'acud';
import React from 'react';
import ReactMonacoEditor from 'react-monaco-editor';

interface IMonacoEditor {
  value?: string;
  language?: string;
  readOnly?: boolean;
  loading?: boolean;
  onChange?: (a: string) => void;
}

/**
 * MonacoEditor编辑器组件
 * @param value 编辑器内容 默认空字符串
 * @param language 编辑器语言 默认json
 * @param readOnly 是否只读 默认false
 * @param loading 是否显示loading 默认false
 * @param onChange 编辑器内容变化回调 默认空函数
 * @returns
 */
const MonacoPage: React.FC<IMonacoEditor> = ({
  // 编辑器内容
  value = '',
  // 编辑器语言
  language = 'json',
  // 是否只读
  readOnly = false,
  loading = false,
  // 编辑器内容变化回调
  onChange
}) => {
  // 编辑器挂载完成 配置主题和语言规则
  const handleEditorDidMount = (editor, monaco) => {
    // 创建自定义主题
    monaco.editor.defineTheme('jsonTheme', {
      base: 'vs',
      inherit: true,
      rules: [
        {token: 'string.key.json', foreground: 'D97116'}, // JSON 键名
        {token: 'string.value.json', foreground: '1B9908'}, // JSON 字符串值
        {token: 'number.json', foreground: '2468F2'}, // JSON 数字
        {token: 'keyword.json', foreground: '8F5CFF'} // JSON 关键字（true, false, null）
      ],
      colors: {
        'editor.background': '#FBFBFC', // 编辑器背景色
        'minimap.background': '#F2F2F4' // 小地图背景色
      }
    });

    // 应用主题
    monaco.editor.setTheme('logTheme');
  };

  return (
    <div
      style={{
        flex: '1',
        minWidth: 0, // 防止内容溢出
        height: '100%',
        display: 'flex'
      }}
    >
      {loading ? (
        <div className="h-full w-full flex items-center justify-center">
          <Loading>
            <div className="w-80 h-80">加载中...</div>
          </Loading>
        </div>
      ) : (
        <ReactMonacoEditor
          theme="jsonTheme"
          height="100%"
          language={language}
          value={value}
          editorDidMount={handleEditorDidMount}
          options={{
            readOnly,
            padding: {top: 8}, // ✅ 设置编辑器上边距为 8px
            scrollBeyondLastLine: false, // 关闭滚动超出最后一行
            automaticLayout: true,
            wordWrap: 'off',
            minimap: {
              enabled: true
            }
          }}
          onChange={onChange}
        />
      )}
    </div>
  );
};

export default MonacoPage;
