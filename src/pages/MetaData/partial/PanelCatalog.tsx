import {useCallback, useContext, useEffect, useMemo, useState} from 'react';
import DescriptionEdit from '@components/DescriptionEdit';
import InfoPanel from '@components/InfoPanel';

import * as http from '@api/metaRequest';
import {CatalogType, IUrlStateHandler} from '../index';
import {WorkspaceContext} from '@pages/index';

import MetaActionBar from '../components/MetaActionBar';
import MetaTabs from '../components/MetaTabs';
import ModalCreateName from '../components/ModalCreateName';
import ModalRemoveFun from '../components/ModalRemoveFun';
import ModalRename from '../components/ModalRename';
import CatalogWorkspace from '../components/CatalogWorkspace';
import IconSvg from '@components/IconSvg';
import {OutlinedPlusNew} from 'acud-icon';
import {RULE} from '@utils/regs';
import {useCookieState} from 'ahooks';
import useAuth from '@hooks/useAuth';

enum PanelEnum {
  DETAIL = '1',
  WORKSPACE = '2'
}

const initialPanes = [
  {tab: '详情', key: PanelEnum.DETAIL},
  {tab: '工作空间', key: PanelEnum.WORKSPACE}
];

const PanelCatalog = (props: IUrlStateHandler) => {
  const {urlState, changeUrlFun, changeUrlFunReplace, handleTreeRefresh, userList} = props;
  const {catalog = '', tab = PanelEnum.DETAIL} = urlState;
  // 元存储管理员权限
  const hasCatalogOperate = useAuth('workspace', 'catalog') === 'readWrite';
  // catalog owner 权限
  const [isOwner, setOwner] = useState<boolean>(false);

  // 获取工作空间ID
  const {workspaceId} = useContext(WorkspaceContext);
  // catalog 全名
  const fullName = `${catalog}`;

  // 获取详情  FIXME: 私有化统一处理下这种公有云获取用户 ID 的处理
  const [userId] = useCookieState('bce-login-userid');
  const getCatalogDetail = async () => {
    const res = await http.getCatalogDetail(fullName);
    setDataInfo(res.result);
    // 当前登录用户是否是当前 Catalog 的 owner
    setOwner(res.result?.catalog?.owner === userId);
  };

  // 初始化
  useEffect(() => {
    catalog && getCatalogDetail();
  }, [catalog]);

  const dropdownMenu = useMemo(() => {
    return catalog
      ? [
          {key: 'rename', label: '重命名 Catalog', disabled: !(hasCatalogOperate || isOwner)},
          {key: 'remove', label: '删除 Catalog', disabled: !(hasCatalogOperate || isOwner)}
        ]
      : [];
  }, [catalog, isOwner, hasCatalogOperate]);

  // 详情 & 概览
  const [dataInfo, setDataInfo] = useState<{
    catalog?: http.ICatalogDetailRes;
    metadataSummary?: http.ICatalogSummaryRes;
  }>({});

  /**
   * 1、默认 system 无工作空间
   * 2、只有【catalog owner】 和【有 catalog 权限的】才可看到到「工作空间tab」
   **/
  const panelsList = useMemo(() => {
    // 过滤掉工作空间
    const list = initialPanes.filter((item) => item.key !== PanelEnum.WORKSPACE);
    if (catalog === CatalogType.SYSTEM) {
      return list;
    } else if (isOwner || hasCatalogOperate) {
      return initialPanes;
    }
    return list;
  }, [catalog, hasCatalogOperate, isOwner]);

  // 详情字段
  const infoList = useMemo(() => {
    const info: any = dataInfo?.catalog || {};
    return [
      {
        label: 'Catalog名称',
        value: info.name
      },
      {
        label: '存储路径',
        value: info.storageLocation
      },
      {
        label: '创建时间',
        value: info.createdAt
      },
      {
        label: '创建人',
        value: userList.find((item) => item.id === info.createdBy)?.name || info.createdBy
      },
      {
        label: '修改时间',
        value: info.updatedAt
      },
      {
        label: '最近修改人',
        value: userList.find((item) => item.id === info.updatedBy)?.name || info.updatedBy
      },
      {
        label: 'Table个数',
        value: `${dataInfo.metadataSummary?.tableCount || 0}个`
      },
      {
        label: 'Volume个数',
        value: `${dataInfo.metadataSummary?.volumeCount || 0}个`
      },
      {
        label: 'Operator个数',
        value: `${dataInfo.metadataSummary?.operatorCount || 0}个`
      }
    ];
  }, [dataInfo, userList]);

  // tab 切换
  const onTabChange = useCallback(
    (tabkey) => {
      changeUrlFun((preState) => ({...preState, tab: tabkey}));
    },
    [changeUrlFun]
  );

  // 更新描述
  const onChangeDescript = useCallback(
    async (text: string) => {
      await http.patchCatalog(fullName, {comment: text});
      getCatalogDetail();
    },
    [dataInfo, setDataInfo]
  );

  // 重命名 & 删除
  const [showRenameModal, setRenameModal] = useState<boolean>(false);
  const renameSuccessFun = useCallback(
    (formData: {name: string}) => {
      changeUrlFunReplace((preState) => ({...preState, catalog: formData.name}), true);
    },
    [changeUrlFunReplace]
  );
  const removeSuccessFun = useCallback(() => {
    changeUrlFunReplace((preState) => ({...preState, catalog: CatalogType.SYSTEM}), true);
  }, [changeUrlFunReplace]);
  const onDropdownClick = useCallback(
    (key) => {
      if (key === 'rename') {
        setRenameModal(true);
      } else if (key === 'remove') {
        ModalRemoveFun({
          fullName,
          name: catalog,
          title: 'Catalog',
          requestFun: http.deleteCatalog,
          successFun: removeSuccessFun
        });
      }
    },
    [catalog, removeSuccessFun]
  );

  // 新建 Schema 弹窗
  const [showCreateModal, setCreateModal] = useState<boolean>(false);
  const onCreateClick = useCallback(() => {
    setCreateModal(true);
  }, []);
  const createSuccessFun = useCallback(
    (formData: {name: string}) => {
      changeUrlFun((preState) => ({...preState, tab: '1', schema: formData.name}));
      handleTreeRefresh && handleTreeRefresh();
    },
    [changeUrlFun]
  );

  return (
    <div className="work-meta-catalog-panel">
      {/* 标题导航操作栏 */}
      <MetaActionBar
        catalog={catalog}
        icon={
          catalog === CatalogType.SYSTEM ? (
            <IconSvg type="meta-system" size={20} color="#fff" />
          ) : (
            <IconSvg type="meta-catalog" size={20} color="#fff" />
          )
        }
        title={catalog}
        dropdownMenu={dropdownMenu}
        onDropdownClick={onDropdownClick}
        createText="创建 Schema"
        createIcon={<OutlinedPlusNew width={16} height={16} />}
        onCreateClick={onCreateClick}
      />
      {/* Tabs */}
      <MetaTabs panesList={panelsList} tab={tab} onTabChange={onTabChange} />
      {/* Tab-Panel 详情 */}
      {tab === PanelEnum.DETAIL ? (
        <>
          <DescriptionEdit
            text={dataInfo?.catalog?.comment || ''}
            onChangeText={onChangeDescript}
            hasEdit={catalog !== CatalogType.SYSTEM && (hasCatalogOperate || isOwner)}
          />

          <InfoPanel infoList={infoList} title="基本信息" />
        </>
      ) : null}
      {/* Tab-Panel 工作空间 */}
      {tab === PanelEnum.WORKSPACE ? (
        <>
          <div className="catalog-panel-workspace">
            <CatalogWorkspace catalog={dataInfo?.catalog} />
          </div>
        </>
      ) : null}
      {/** 创建 Schema 弹窗 */}
      <ModalCreateName
        visible={showCreateModal}
        setVisible={setCreateModal}
        title="Schema"
        requestFun={http.createSchema}
        requestParams={{catalogName: catalog}}
        successFun={createSuccessFun}
        nameRules={[
          {
            validator: async (_, value) => {
              // 校验特殊字符和长度限制
              if (!RULE.specialName64.test(value)) {
                return Promise.reject(new Error(RULE.specialName64Text));
              }
              // 异步校验Volume名称是否重复，复用查询接口 silent模式
              const res = await http.getSchemaDetail(`${catalog}.${value}`, true);
              if (res.success && res.result?.schema?.id) {
                return Promise.reject(new Error('该Schema名称已存在，请重新输入'));
              }
              return Promise.resolve();
            }
          }
        ]}
      />
      {/** 重命名 Catalog 弹窗 */}
      <ModalRename
        visible={showRenameModal}
        setVisible={setRenameModal}
        fullName={fullName}
        nowValue={catalog}
        title="Catalog"
        requestFun={http.patchCatalog}
        successFun={renameSuccessFun}
        nameRules={[
          {
            validator: async (_, value) => {
              // 校验特殊字符和长度限制
              if (!RULE.specialName64.test(value)) {
                return Promise.reject(new Error(RULE.specialName64Text));
              }
              // 异步校验Volume名称是否重复，复用查询接口 silent模式
              const res = await http.getCatalogDetail(`${value}`, true);
              if (res.success && res.result?.catalog?.id) {
                return Promise.reject(new Error('该Catalog名称已存在，请重新输入'));
              }
              return Promise.resolve();
            }
          }
        ]}
      />
    </div>
  );
};
export default PanelCatalog;
